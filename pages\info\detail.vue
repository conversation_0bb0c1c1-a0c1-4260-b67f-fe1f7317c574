<template>
  <view class="detail-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="详情" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" overlay text="加载中..." />
    
    <!-- 错误状态 -->
    <EmptyState 
      v-else-if="error"
      type="no-network"
      :title="error"
      description="请检查网络连接后重试"
      :showButton="true"
      buttonText="重新加载"
      @buttonClick="loadDetail"
    />
    
    <!-- 详情内容 -->
    <view v-else-if="detail" class="detail-content">
      <!-- 文章头部 -->
      <view class="article-header">
        <view class="header-tags">
          <StatusTag :type="'announcement'" :status="detail.type" />
          <view v-if="detail.isImportant" class="important-tag">
            <u-icon name="warning" color="#FF9500" size="24" />
            <text>重要</text>
          </view>
          <view v-if="detail.isTop" class="top-tag">
            <u-icon name="arrow-up" color="#52C41A" size="24" />
            <text>置顶</text>
          </view>
        </view>
        
        <text class="article-title">{{ detail.title }}</text>
        
        <view class="article-meta">
          <view class="meta-item">
            <u-icon name="calendar" color="#999" size="28" />
            <text>{{ formatDate(detail.publishTime, 'YYYY-MM-DD HH:mm') }}</text>
          </view>
          <view v-if="detail.source" class="meta-item">
            <u-icon name="home" color="#999" size="28" />
            <text>{{ detail.source }}</text>
          </view>
        </view>
      </view>
      
      <!-- 分割线 -->
      <view class="divider"></view>
      
      <!-- 文章内容 -->
      <view class="article-body">
        <rich-text :nodes="formattedContent" class="rich-content" />
      </view>
      
      <!-- 底部操作 -->
      <view class="article-actions">
        <view class="action-item" @click="handleShare">
          <u-icon name="share" color="#666" size="32" />
          <text>分享</text>
        </view>
        <view class="action-item" @click="handleCollect">
          <u-icon :name="isCollected ? 'heart-fill' : 'heart'" :color="isCollected ? '#F5222D' : '#666'" size="32" />
          <text>{{ isCollected ? '已收藏' : '收藏' }}</text>
        </view>
        <view class="action-item" @click="handleFeedback">
          <u-icon name="chat" color="#666" size="32" />
          <text>反馈</text>
        </view>
      </view>
    </view>
    
    <!-- 相关推荐 -->
    <view v-if="relatedList.length > 0" class="related-section">
      <view class="section-title">
        <text>相关推荐</text>
      </view>
      
      <view class="related-list">
        <view 
          v-for="item in relatedList" 
          :key="item.id"
          class="related-item"
          @click="viewRelated(item)"
        >
          <text class="related-title">{{ item.title }}</text>
          <text class="related-date">{{ formatRelativeTime(item.publishTime) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '../../src/stores/app'
import api from '../../src/api'
import { formatDate, formatRelativeTime } from '../../src/utils'
import type { Announcement } from '../../src/types'

// 导入组件
import LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'
import EmptyState from '../../src/components/common/EmptyState.vue'
import StatusTag from '../../src/components/common/StatusTag.vue'

// Store
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  id: string
}>()

// 响应式数据
const isLoading = ref(true)
const error = ref('')
const detail = ref<Announcement | null>(null)
const relatedList = ref<Announcement[]>([])
const isCollected = ref(false)

// 格式化内容
const formattedContent = computed(() => {
  if (!detail.value?.content) return ''
  
  // 简单的HTML内容处理
  let content = detail.value.content
  
  // 替换换行符
  content = content.replace(/\n/g, '<br/>')
  
  // 添加段落样式
  content = content.replace(/(<br\/>){2,}/g, '</p><p>')
  content = `<p>${content}</p>`
  
  return content
})

onMounted(() => {
  loadDetail()
  loadRelated()
})

// 加载详情
const loadDetail = async () => {
  if (!props.id) {
    error.value = '参数错误'
    isLoading.value = false
    return
  }
  
  isLoading.value = true
  error.value = ''
  
  try {
    const response = await api.info.getAnnouncementDetail(props.id)
    detail.value = response.data
    
    // 检查是否已收藏（这里可以调用收藏状态API）
    // const collectResponse = await api.info.getCollectStatus(props.id)
    // isCollected.value = collectResponse.data.isCollected
  } catch (err: any) {
    console.error('加载详情失败:', err)
    error.value = err.message || '加载失败'
  } finally {
    isLoading.value = false
  }
}

// 加载相关推荐
const loadRelated = async () => {
  try {
    const response = await api.info.getAnnouncements({
      page: 1,
      pageSize: 5,
      type: detail.value?.type
    })
    
    // 过滤掉当前文章
    relatedList.value = response.data.list.filter(item => item.id !== props.id)
  } catch (error) {
    console.error('加载相关推荐失败:', error)
  }
}

// 查看相关文章
const viewRelated = (item: Announcement) => {
  // 替换当前页面
  uni.redirectTo({
    url: `/pages/info/detail?id=${item.id}`
  })
}

// 分享
const handleShare = () => {
  if (!detail.value) return
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '', // 这里应该是文章的分享链接
    title: detail.value.title,
    summary: detail.value.content.substring(0, 100),
    imageUrl: '', // 分享图片
    success: () => {
      appStore.showToast('分享成功', 'success')
    },
    fail: (err) => {
      console.error('分享失败:', err)
      appStore.showToast('分享失败')
    }
  })
}

// 收藏/取消收藏
const handleCollect = async () => {
  if (!detail.value) return
  
  try {
    if (isCollected.value) {
      // 取消收藏
      // await api.info.uncollect(detail.value.id)
      isCollected.value = false
      appStore.showToast('已取消收藏', 'success')
    } else {
      // 收藏
      // await api.info.collect(detail.value.id)
      isCollected.value = true
      appStore.showToast('收藏成功', 'success')
    }
  } catch (error: any) {
    appStore.showToast(error.message || '操作失败')
  }
}

// 反馈
const handleFeedback = () => {
  appStore.showModal({
    title: '问题反馈',
    content: '如果您发现内容有误或有其他问题，请联系我们进行反馈。',
    confirmText: '联系客服',
    cancelText: '取消'
  }).then((confirmed) => {
    if (confirmed) {
      // 跳转到反馈页面或联系客服
      appStore.showToast('功能开发中')
    }
  })
}
</script>

<style lang="scss" scoped>
@import '../../src/styles/global.scss';

.detail-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.detail-content {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.article-header {
  padding: 40rpx;
  
  .header-tags {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    
    .important-tag,
    .top-tag {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      
      text {
        font-weight: bold;
      }
    }
    
    .important-tag {
      background: #fff7e6;
      color: $uni-color-warning;
    }
    
    .top-tag {
      background: #f6ffed;
      color: $uni-color-success;
    }
  }
  
  .article-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: $acdc-text-primary;
    line-height: 1.4;
    margin-bottom: 32rpx;
  }
  
  .article-meta {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 12rpx;
      
      text {
        font-size: 26rpx;
        color: $acdc-text-secondary;
      }
    }
  }
}

.divider {
  height: 2rpx;
  background: $acdc-divider-color;
  margin: 0 40rpx;
}

.article-body {
  padding: 40rpx;
  
  .rich-content {
    font-size: 30rpx;
    line-height: 1.8;
    color: $acdc-text-primary;
    
    :deep(p) {
      margin-bottom: 24rpx;
      text-align: justify;
    }
    
    :deep(img) {
      max-width: 100%;
      height: auto;
      border-radius: 8rpx;
      margin: 16rpx 0;
    }
    
    :deep(strong) {
      font-weight: bold;
      color: $acdc-primary;
    }
  }
}

.article-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 40rpx;
  border-top: 2rpx solid $acdc-divider-color;
  
  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx;
    
    text {
      font-size: 24rpx;
      color: $acdc-text-secondary;
    }
  }
}

.related-section {
  margin: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-title {
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 2rpx solid $acdc-divider-color;
    
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: $acdc-text-primary;
    }
  }
  
  .related-list {
    padding: 24rpx 40rpx 40rpx;
    
    .related-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 24rpx 0;
      border-bottom: 1rpx solid $acdc-divider-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .related-title {
        flex: 1;
        font-size: 28rpx;
        color: $acdc-text-primary;
        line-height: 1.4;
        margin-right: 24rpx;
        
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .related-date {
        font-size: 24rpx;
        color: $acdc-text-disabled;
        white-space: nowrap;
      }
    }
  }
}
</style>
