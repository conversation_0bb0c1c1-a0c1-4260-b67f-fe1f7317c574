import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UserInfo {
  id?: string
  nickName?: string
  avatarUrl?: string
  realName?: string
  idCard?: string
  phone?: string
  organization?: string
  position?: string
  faceImage?: string
  gender?: number
  birthday?: string
  address?: string
}

export const useUserStore = defineStore('user', () => {
  // 用户基本信息
  const userInfo = ref<UserInfo>({})
  
  // 认证状态：not_submitted（未提交）, pending（审核中）, verified（已认证）, rejected（审核未通过）
  const authStatus = ref<'not_submitted' | 'pending' | 'verified' | 'rejected'>('not_submitted')
  
  // 是否已登录
  const isLoggedIn = ref(false)
  
  // 微信登录信息
  const wxUserInfo = ref({
    nickName: '',
    avatarUrl: '',
    openId: '',
    unionId: ''
  })
  
  // 权限相关计算属性
  const isVerified = computed(() => authStatus.value === 'verified')
  const isPending = computed(() => authStatus.value === 'pending')
  const isRejected = computed(() => authStatus.value === 'rejected')
  const needSubmitInfo = computed(() => authStatus.value === 'not_submitted')
  
  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = { ...userInfo.value, ...info }
  }
  
  // 设置认证状态
  const setAuthStatus = (status: typeof authStatus.value) => {
    authStatus.value = status
  }
  
  // 设置登录状态
  const setLoginStatus = (status: boolean) => {
    isLoggedIn.value = status
  }
  
  // 设置微信用户信息
  const setWxUserInfo = (info: typeof wxUserInfo.value) => {
    wxUserInfo.value = { ...wxUserInfo.value, ...info }
  }
  
  // 微信登录
  const wxLogin = async () => {
    try {
      // 调用微信登录API
      const loginRes = await uni.login({
        provider: 'weixin'
      })
      
      if (loginRes.code) {
        // 这里应该调用后端API，用code换取用户信息
        console.log('微信登录成功，code:', loginRes.code)
        
        // 模拟获取用户信息
        const mockUserInfo = {
          nickName: '微信用户',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=center',
          openId: 'mock_open_id',
          unionId: 'mock_union_id'
        }
        
        setWxUserInfo(mockUserInfo)
        setLoginStatus(true)
        
        return {
          success: true,
          userInfo: mockUserInfo
        }
      } else {
        throw new Error('获取微信授权码失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return {
        success: false,
        error: error.message || '登录失败'
      }
    }
  }
  
  // 提交个人信息
  const submitPersonalInfo = async (info: UserInfo) => {
    try {
      // 这里应该调用后端API提交个人信息
      console.log('提交个人信息:', info)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setUserInfo(info)
      setAuthStatus('pending')
      
      return {
        success: true,
        message: '个人信息提交成功，请等待审核'
      }
    } catch (error) {
      console.error('提交个人信息失败:', error)
      return {
        success: false,
        error: error.message || '提交失败'
      }
    }
  }
  
  // 检查认证状态
  const checkAuthStatus = async () => {
    try {
      // 这里应该调用后端API检查用户认证状态
      console.log('检查认证状态')
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟不同的认证状态
      const mockStatus = 'verified' // 可以是 'not_submitted', 'pending', 'verified', 'rejected'
      setAuthStatus(mockStatus)
      
      return {
        success: true,
        status: mockStatus
      }
    } catch (error) {
      console.error('检查认证状态失败:', error)
      return {
        success: false,
        error: error.message || '检查失败'
      }
    }
  }
  
  // 退出登录
  const logout = () => {
    userInfo.value = {}
    authStatus.value = 'not_submitted'
    isLoggedIn.value = false
    wxUserInfo.value = {
      nickName: '',
      avatarUrl: '',
      openId: '',
      unionId: ''
    }
    
    // 清除本地存储
    try {
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('authStatus')
      uni.removeStorageSync('wxUserInfo')
    } catch (error) {
      console.error('清除本地存储失败:', error)
    }
  }
  
  // 初始化用户数据（从本地存储恢复）
  const initUserData = () => {
    try {
      const savedUserInfo = uni.getStorageSync('userInfo')
      const savedAuthStatus = uni.getStorageSync('authStatus')
      const savedWxUserInfo = uni.getStorageSync('wxUserInfo')
      
      if (savedUserInfo) {
        setUserInfo(savedUserInfo)
      }
      
      if (savedAuthStatus) {
        setAuthStatus(savedAuthStatus)
      }
      
      if (savedWxUserInfo) {
        setWxUserInfo(savedWxUserInfo)
        setLoginStatus(true)
      }
    } catch (error) {
      console.error('初始化用户数据失败:', error)
    }
  }
  
  // 保存用户数据到本地存储
  const saveUserData = () => {
    try {
      uni.setStorageSync('userInfo', userInfo.value)
      uni.setStorageSync('authStatus', authStatus.value)
      uni.setStorageSync('wxUserInfo', wxUserInfo.value)
    } catch (error) {
      console.error('保存用户数据失败:', error)
    }
  }
  
  return {
    // 状态
    userInfo,
    authStatus,
    isLoggedIn,
    wxUserInfo,
    
    // 计算属性
    isVerified,
    isPending,
    isRejected,
    needSubmitInfo,
    
    // 方法
    setUserInfo,
    setAuthStatus,
    setLoginStatus,
    setWxUserInfo,
    wxLogin,
    submitPersonalInfo,
    checkAuthStatus,
    logout,
    initUserData,
    saveUserData
  }
}) 