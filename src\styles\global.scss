/**
 * 全局样式文件
 * 疾控医护考试系统
 */

// 注意：主题变量已在App.vue中导入，此处不再重复导入

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: $acdc-bg-primary;
  color: $acdc-text-primary;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 通用布局类 */
.container {
  padding: 24rpx;
  min-height: 100vh;
}

.page-container {
  padding: 32rpx 24rpx;
  background-color: $acdc-bg-primary;
  min-height: 100vh;
}

.content-container {
  background-color: $acdc-bg-light;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 文字样式 */
.text-primary {
  color: $acdc-text-primary;
}

.text-secondary {
  color: $acdc-text-secondary;
}

.text-disabled {
  color: $acdc-text-disabled;
}

.text-success {
  color: $uni-color-success;
}

.text-warning {
  color: $uni-color-warning;
}

.text-error {
  color: $uni-color-error;
}

.text-primary-color {
  color: $acdc-primary;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-normal {
  font-weight: normal;
}

/* 字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 40rpx;
}

.text-3xl {
  font-size: 48rpx;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }
.m-5 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }
.mt-5 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }
.mb-5 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8rpx; }
.ml-2 { margin-left: 16rpx; }
.ml-3 { margin-left: 24rpx; }
.ml-4 { margin-left: 32rpx; }
.ml-5 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8rpx; }
.mr-2 { margin-right: 16rpx; }
.mr-3 { margin-right: 24rpx; }
.mr-4 { margin-right: 32rpx; }
.mr-5 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-5 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }
.pt-5 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }
.pb-5 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8rpx; }
.pl-2 { padding-left: 16rpx; }
.pl-3 { padding-left: 24rpx; }
.pl-4 { padding-left: 32rpx; }
.pl-5 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8rpx; }
.pr-2 { padding-right: 16rpx; }
.pr-3 { padding-right: 24rpx; }
.pr-4 { padding-right: 32rpx; }
.pr-5 { padding-right: 40rpx; }

/* 宽高 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-screen {
  width: 100vw;
}

.h-screen {
  height: 100vh;
}

/* 圆角 */
.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: 4rpx;
}

.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-xl {
  border-radius: 24rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.shadow {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shadow-lg {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

/* 边框 */
.border {
  border: 1rpx solid $acdc-border-color;
}

.border-t {
  border-top: 1rpx solid $acdc-border-color;
}

.border-b {
  border-bottom: 1rpx solid $acdc-border-color;
}

.border-l {
  border-left: 1rpx solid $acdc-border-color;
}

.border-r {
  border-right: 1rpx solid $acdc-border-color;
}

/* 背景色 */
.bg-primary {
  background-color: $acdc-primary;
}

.bg-light {
  background-color: $acdc-bg-light;
}

.bg-gray {
  background-color: $acdc-bg-primary;
}

.bg-success {
  background-color: $uni-color-success;
}

.bg-warning {
  background-color: $uni-color-warning;
}

.bg-error {
  background-color: $uni-color-error;
}

/* 通用组件样式 */
.card {
  background-color: $acdc-bg-light;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.divider {
  height: 1rpx;
  background-color: $acdc-divider-color;
  margin: 24rpx 0;
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 状态样式 */
.status-pending {
  color: $uni-color-warning;
}

.status-approved {
  color: $uni-color-success;
}

.status-rejected {
  color: $uni-color-error;
}

.status-not-submitted {
  color: $acdc-text-secondary;
}

/* 按钮样式扩展 */
.btn-primary {
  background-color: $acdc-primary;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}

.btn-secondary {
  background-color: transparent;
  color: $acdc-primary;
  border: 1rpx solid $acdc-primary;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

.btn-ghost {
  background-color: transparent;
  color: $acdc-text-secondary;
  border: 1rpx solid $acdc-border-color;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
