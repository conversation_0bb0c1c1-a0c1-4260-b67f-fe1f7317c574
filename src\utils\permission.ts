import { useUserStore } from '../stores/user'
import { useAppStore } from '../stores/app'
import { PAGE_PATHS } from '../constants'
import type { UserStatus } from '../types'

/**
 * 权限控制工具类
 */
export class PermissionManager {
  private userStore = useUserStore()
  private appStore = useAppStore()

  /**
   * 检查用户是否已登录
   */
  isLoggedIn(): boolean {
    return this.userStore.isLoggedIn
  }

  /**
   * 检查用户是否已认证（通过审核）
   */
  isAuthenticated(): boolean {
    return this.userStore.isAuthenticated
  }

  /**
   * 检查用户状态
   */
  getUserStatus(): UserStatus | null {
    return this.userStore.userInfo?.status || null
  }

  /**
   * 检查是否可以访问考试功能
   */
  canAccessExam(): boolean {
    return this.userStore.canAccessExam
  }

  /**
   * 检查是否可以访问完整功能
   */
  canAccessFullFeatures(): boolean {
    return this.userStore.canAccessFullFeatures
  }

  /**
   * 页面访问权限检查
   */
  checkPageAccess(pagePath: string): {
    allowed: boolean
    redirectTo?: string
    message?: string
  } {
    // 公开页面，无需权限检查
    const publicPages = [
      PAGE_PATHS.LOGIN,
      PAGE_PATHS.PROFILE,
    ]

    if (publicPages.includes(pagePath)) {
      return { allowed: true }
    }

    // 检查是否已登录
    if (!this.isLoggedIn()) {
      return {
        allowed: false,
        redirectTo: PAGE_PATHS.LOGIN,
        message: '请先登录'
      }
    }

    // 需要认证的页面
    const authRequiredPages = [
      PAGE_PATHS.EXAM,
      PAGE_PATHS.EXAM_ONLINE_READING,
      PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
      PAGE_PATHS.EXAM_ONLINE_ANSWER,
      PAGE_PATHS.EXAM_OFFLINE_DETAIL,
      PAGE_PATHS.EXAM_HISTORY,
    ]

    if (authRequiredPages.includes(pagePath)) {
      if (!this.isAuthenticated()) {
        return {
          allowed: false,
          redirectTo: PAGE_PATHS.PERSONAL,
          message: '请先完善个人资料并通过审核'
        }
      }
    }

    // 部分功能页面（未认证用户可访问但功能受限）
    const partialAccessPages = [
      PAGE_PATHS.INFO,
      PAGE_PATHS.STUDY,
      PAGE_PATHS.PERSONAL,
    ]

    if (partialAccessPages.includes(pagePath)) {
      return { allowed: true }
    }

    return { allowed: true }
  }

  /**
   * 功能权限检查
   */
  checkFeatureAccess(feature: string): {
    allowed: boolean
    message?: string
  } {
    const userStatus = this.getUserStatus()

    switch (feature) {
      case 'practice':
        // 练习功能：所有用户都可以使用，但有次数限制
        return { allowed: true }

      case 'exam':
        // 考试功能：只有认证用户可以使用
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: '请先完善个人资料并通过机构审核后才能参加考试'
          }
        }
        return { allowed: true }

      case 'certificate':
        // 证书功能：只有认证用户可以使用
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: '请先完善个人资料并通过机构审核'
          }
        }
        return { allowed: true }

      case 'profile_edit':
        // 资料编辑：待审核和已认证用户不能编辑基本信息
        if (userStatus === 'pending' || userStatus === 'approved') {
          return {
            allowed: false,
            message: '资料审核中或已通过审核，无法修改基本信息'
          }
        }
        return { allowed: true }

      case 'unlimited_practice':
        // 无限练习：只有认证用户可以使用
        if (!this.isAuthenticated()) {
          return {
            allowed: false,
            message: '认证用户可享受无限练习，请先完善资料并通过审核'
          }
        }
        return { allowed: true }

      default:
        return { allowed: true }
    }
  }

  /**
   * 执行权限检查并处理结果
   */
  async enforcePageAccess(pagePath: string): Promise<boolean> {
    const result = this.checkPageAccess(pagePath)

    if (!result.allowed) {
      if (result.message) {
        this.appStore.showToast(result.message)
      }

      if (result.redirectTo) {
        // 延迟跳转，确保toast显示
        setTimeout(() => {
          if (result.redirectTo === PAGE_PATHS.LOGIN) {
            this.appStore.redirectTo(result.redirectTo)
          } else {
            this.appStore.switchTab(result.redirectTo)
          }
        }, 1500)
      }

      return false
    }

    return true
  }

  /**
   * 执行功能权限检查
   */
  enforceFeatureAccess(feature: string): boolean {
    const result = this.checkFeatureAccess(feature)

    if (!result.allowed && result.message) {
      this.appStore.showToast(result.message)
    }

    return result.allowed
  }

  /**
   * 获取用户状态提示信息
   */
  getUserStatusMessage(): string {
    const userStatus = this.getUserStatus()

    switch (userStatus) {
      case 'not_submitted':
        return '请完善个人资料以使用完整功能'
      case 'pending':
        return '个人资料审核中，请耐心等待'
      case 'approved':
        return '已通过认证，可使用所有功能'
      case 'rejected':
        return '资料审核未通过，请重新提交'
      default:
        return '请先登录'
    }
  }

  /**
   * 获取功能限制提示
   */
  getFeatureLimitMessage(feature: string): string {
    switch (feature) {
      case 'practice':
        if (!this.isAuthenticated()) {
          return '未认证用户每日限制3次练习'
        }
        return '认证用户可无限练习'
      case 'exam':
        return '需要通过机构审核后才能参加正式考试'
      case 'certificate':
        return '需要通过考试后才能获得证书'
      default:
        return ''
    }
  }
}

// 创建全局权限管理器实例
export const permissionManager = new PermissionManager()

// 页面权限检查装饰器
export function requireAuth(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any[]) {
    if (!permissionManager.isLoggedIn()) {
      permissionManager.appStore.showToast('请先登录')
      permissionManager.appStore.redirectTo(PAGE_PATHS.LOGIN)
      return
    }

    return originalMethod.apply(this, args)
  }

  return descriptor
}

// 认证权限检查装饰器
export function requireAuthentication(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any[]) {
    if (!permissionManager.isAuthenticated()) {
      permissionManager.appStore.showToast('请先完善个人资料并通过审核')
      permissionManager.appStore.switchTab(PAGE_PATHS.PERSONAL)
      return
    }

    return originalMethod.apply(this, args)
  }

  return descriptor
}

// 导出权限检查函数
export const checkAuth = () => permissionManager.isLoggedIn()
export const checkAuthentication = () => permissionManager.isAuthenticated()
export const checkPageAccess = (pagePath: string) => permissionManager.checkPageAccess(pagePath)
export const checkFeatureAccess = (feature: string) => permissionManager.checkFeatureAccess(feature)
export const enforcePageAccess = (pagePath: string) => permissionManager.enforcePageAccess(pagePath)
export const enforceFeatureAccess = (feature: string) => permissionManager.enforceFeatureAccess(feature)
