<template>
  <view class="loading-spinner" :class="{ 'loading-overlay': overlay }">
    <view class="spinner-container">
      <view class="spinner" :class="sizeClass">
        <view class="spinner-dot" v-for="i in 12" :key="i" :style="{ transform: `rotate(${i * 30}deg)` }">
          <view class="dot"></view>
        </view>
      </view>
      <text v-if="text" class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'small' | 'medium' | 'large'
  text?: string
  overlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  text: '',
  overlay: false,
})

const sizeClass = computed(() => {
  return `spinner-${props.size}`
})
</script>

<style lang="scss" scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  position: relative;
  animation: spin 1.2s linear infinite;
}

.spinner-small {
  width: 40rpx;
  height: 40rpx;
}

.spinner-medium {
  width: 60rpx;
  height: 60rpx;
}

.spinner-large {
  width: 80rpx;
  height: 80rpx;
}

.spinner-dot {
  position: absolute;
  top: 0;
  left: 50%;
  transform-origin: 0 50%;
  animation: fade 1.2s linear infinite;
}

.spinner-small .spinner-dot {
  width: 6rpx;
  height: 20rpx;
  margin-left: -3rpx;
}

.spinner-medium .spinner-dot {
  width: 8rpx;
  height: 30rpx;
  margin-left: -4rpx;
}

.spinner-large .spinner-dot {
  width: 10rpx;
  height: 40rpx;
  margin-left: -5rpx;
}

.dot {
  width: 100%;
  height: 25%;
  background-color: #2E8B57;
  border-radius: 50rpx;
}

.spinner-dot:nth-child(1) { animation-delay: 0s; }
.spinner-dot:nth-child(2) { animation-delay: -0.1s; }
.spinner-dot:nth-child(3) { animation-delay: -0.2s; }
.spinner-dot:nth-child(4) { animation-delay: -0.3s; }
.spinner-dot:nth-child(5) { animation-delay: -0.4s; }
.spinner-dot:nth-child(6) { animation-delay: -0.5s; }
.spinner-dot:nth-child(7) { animation-delay: -0.6s; }
.spinner-dot:nth-child(8) { animation-delay: -0.7s; }
.spinner-dot:nth-child(9) { animation-delay: -0.8s; }
.spinner-dot:nth-child(10) { animation-delay: -0.9s; }
.spinner-dot:nth-child(11) { animation-delay: -1.0s; }
.spinner-dot:nth-child(12) { animation-delay: -1.1s; }

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #8A8A8A;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fade {
  0%, 39%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}
</style>
