<template>
  <view class="permission-wrapper">
    <!-- 有权限时显示内容 -->
    <slot v-if="hasPermission" />
    
    <!-- 无权限时显示提示 -->
    <view v-else-if="showFallback" class="permission-fallback">
      <view class="fallback-content">
        <u-icon :name="fallbackIcon" :color="fallbackIconColor" size="80" />
        <text class="fallback-title">{{ fallbackTitle }}</text>
        <text class="fallback-message">{{ fallbackMessage }}</text>
        <u-button 
          v-if="showAction"
          type="primary"
          size="normal"
          @click="handleAction"
          class="fallback-action"
        >
          {{ actionText }}
        </u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '../../stores/user'
import { useAppStore } from '../../stores/app'
import { permissionManager } from '../../utils/permission'
import { PAGE_PATHS } from '../../constants'

interface Props {
  // 权限类型
  permission?: 'auth' | 'authenticated' | 'feature'
  // 功能名称（当permission为feature时使用）
  feature?: string
  // 是否显示无权限提示
  showFallback?: boolean
  // 自定义提示信息
  fallbackTitle?: string
  fallbackMessage?: string
  fallbackIcon?: string
  fallbackIconColor?: string
  // 是否显示操作按钮
  showAction?: boolean
  actionText?: string
  actionType?: 'login' | 'profile' | 'custom'
}

const props = withDefaults(defineProps<Props>(), {
  permission: 'auth',
  feature: '',
  showFallback: true,
  fallbackTitle: '',
  fallbackMessage: '',
  fallbackIcon: 'lock',
  fallbackIconColor: '#BFBFBF',
  showAction: true,
  actionText: '',
  actionType: 'login'
})

const emit = defineEmits<{
  action: []
}>()

const userStore = useUserStore()
const appStore = useAppStore()

// 计算是否有权限
const hasPermission = computed(() => {
  switch (props.permission) {
    case 'auth':
      return permissionManager.isLoggedIn()
    case 'authenticated':
      return permissionManager.isAuthenticated()
    case 'feature':
      if (!props.feature) return true
      return permissionManager.checkFeatureAccess(props.feature).allowed
    default:
      return true
  }
})

// 计算提示标题
const fallbackTitle = computed(() => {
  if (props.fallbackTitle) return props.fallbackTitle
  
  switch (props.permission) {
    case 'auth':
      return '需要登录'
    case 'authenticated':
      return '需要认证'
    case 'feature':
      return '功能受限'
    default:
      return '权限不足'
  }
})

// 计算提示信息
const fallbackMessage = computed(() => {
  if (props.fallbackMessage) return props.fallbackMessage
  
  switch (props.permission) {
    case 'auth':
      return '请先登录后使用此功能'
    case 'authenticated':
      return permissionManager.getUserStatusMessage()
    case 'feature':
      if (props.feature) {
        const result = permissionManager.checkFeatureAccess(props.feature)
        return result.message || '您暂时无法使用此功能'
      }
      return '您暂时无法使用此功能'
    default:
      return '您暂时无法使用此功能'
  }
})

// 计算操作按钮文本
const actionText = computed(() => {
  if (props.actionText) return props.actionText
  
  switch (props.actionType) {
    case 'login':
      return '立即登录'
    case 'profile':
      return '完善资料'
    default:
      return '了解详情'
  }
})

// 处理操作按钮点击
const handleAction = () => {
  switch (props.actionType) {
    case 'login':
      appStore.redirectTo(PAGE_PATHS.LOGIN)
      break
    case 'profile':
      if (userStore.userInfo?.status === 'not_submitted') {
        appStore.redirectTo(PAGE_PATHS.PROFILE)
      } else {
        appStore.switchTab(PAGE_PATHS.PERSONAL)
      }
      break
    case 'custom':
      emit('action')
      break
    default:
      emit('action')
  }
}
</script>

<style lang="scss" scoped>
.permission-wrapper {
  width: 100%;
}

.permission-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 400rpx;
}

.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400rpx;
}

.fallback-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8A8A8A;
  margin: 32rpx 0 16rpx;
}

.fallback-message {
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.fallback-action {
  margin-top: 16rpx;
}
</style>
