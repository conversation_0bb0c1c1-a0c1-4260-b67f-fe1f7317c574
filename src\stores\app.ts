import { defineStore } from 'pinia'
import { ref } from 'vue'
import { STORAGE_KEYS } from '../constants'

export interface AppSettings {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  enableNotifications: boolean
  enableSounds: boolean
  practiceReminder: boolean
  examReminder: boolean
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref<boolean>(false)
  const networkStatus = ref<boolean>(true)
  const systemInfo = ref<any>(null)
  const settings = ref<AppSettings>({
    theme: 'light',
    language: 'zh-CN',
    enableNotifications: true,
    enableSounds: true,
    practiceReminder: true,
    examReminder: true,
  })

  // 全局加载状态
  const setLoading = (status: boolean) => {
    loading.value = status
  }

  // 网络状态
  const setNetworkStatus = (status: boolean) => {
    networkStatus.value = status
  }

  // 系统信息
  const setSystemInfo = (info: any) => {
    systemInfo.value = info
  }

  // 获取系统信息
  const getSystemInfo = async () => {
    try {
      const info = await uni.getSystemInfo()
      setSystemInfo(info[1])
      return info[1]
    } catch (error) {
      console.error('Failed to get system info:', error)
      return null
    }
  }

  // 设置配置
  const updateSettings = (newSettings: Partial<AppSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    saveSettings()
  }

  // 保存设置到本地存储
  const saveSettings = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.SETTINGS, JSON.stringify(settings.value))
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  // 从本地存储加载设置
  const loadSettings = () => {
    try {
      const storedSettings = uni.getStorageSync(STORAGE_KEYS.SETTINGS)
      if (storedSettings) {
        settings.value = { ...settings.value, ...JSON.parse(storedSettings) }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  // 显示Toast
  const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {
    uni.showToast({
      title,
      icon,
      duration: 2000,
    })
  }

  // 显示加载提示
  const showLoading = (title: string = '加载中...') => {
    setLoading(true)
    uni.showLoading({ title })
  }

  // 隐藏加载提示
  const hideLoading = () => {
    setLoading(false)
    uni.hideLoading()
  }

  // 显示模态框
  const showModal = (options: {
    title?: string
    content: string
    showCancel?: boolean
    confirmText?: string
    cancelText?: string
  }) => {
    return new Promise<boolean>((resolve) => {
      uni.showModal({
        title: options.title || '提示',
        content: options.content,
        showCancel: options.showCancel !== false,
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        },
      })
    })
  }

  // 页面跳转
  const navigateTo = (url: string, params?: Record<string, any>) => {
    let fullUrl = url
    if (params) {
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      fullUrl += `?${query}`
    }
    
    uni.navigateTo({ url: fullUrl })
  }

  // 页面重定向
  const redirectTo = (url: string, params?: Record<string, any>) => {
    let fullUrl = url
    if (params) {
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      fullUrl += `?${query}`
    }
    
    uni.redirectTo({ url: fullUrl })
  }

  // 切换Tab页面
  const switchTab = (url: string) => {
    uni.switchTab({ url })
  }

  // 返回上一页
  const navigateBack = (delta: number = 1) => {
    uni.navigateBack({ delta })
  }

  // 检查网络状态
  const checkNetworkStatus = () => {
    uni.getNetworkType({
      success: (res) => {
        setNetworkStatus(res.networkType !== 'none')
      },
      fail: () => {
        setNetworkStatus(false)
      },
    })
  }

  // 监听网络状态变化
  const watchNetworkStatus = () => {
    uni.onNetworkStatusChange((res) => {
      setNetworkStatus(res.isConnected)
    })
  }

  // 初始化应用
  const initApp = async () => {
    // 加载设置
    loadSettings()
    
    // 获取系统信息
    await getSystemInfo()
    
    // 检查网络状态
    checkNetworkStatus()
    
    // 监听网络状态变化
    watchNetworkStatus()
  }

  return {
    // 状态
    loading,
    networkStatus,
    systemInfo,
    settings,
    
    // 方法
    setLoading,
    setNetworkStatus,
    setSystemInfo,
    getSystemInfo,
    updateSettings,
    saveSettings,
    loadSettings,
    showToast,
    showLoading,
    hideLoading,
    showModal,
    navigateTo,
    redirectTo,
    switchTab,
    navigateBack,
    checkNetworkStatus,
    watchNetworkStatus,
    initApp,
  }
})
