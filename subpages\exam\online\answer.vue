<template>
  <view class="exam-answer-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      :title="examTitle" 
      :autoBack="false"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 考试状态栏 -->
    <view class="exam-status-bar">
      <view class="status-info">
        <text class="question-progress">{{ currentQuestionIndex + 1 }}/{{ totalQuestions }}</text>
        <text class="time-remaining" :class="{ warning: timeRemaining <= 300 }">
          {{ formatTime(timeRemaining) }}
        </text>
      </view>
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        ></view>
      </view>
    </view>
    
    <!-- 题目内容 -->
    <view v-if="currentQuestion" class="question-container">
      <scroll-view class="question-scroll" scroll-y>
        <view class="question-header">
          <view class="question-info">
            <StatusTag :type="'question'" :status="currentQuestion.type" />
            <text class="question-score">{{ currentQuestion.score || 5 }}分</text>
          </view>
        </view>
        
        <view class="question-content">
          <text class="question-title">{{ currentQuestion.title }}</text>
        </view>
        
        <!-- 选择题选项 -->
        <view v-if="isChoiceQuestion" class="question-options">
          <view 
            v-for="(option, index) in currentQuestion.options" 
            :key="index"
            class="option-item"
            :class="getOptionClass(index)"
            @click="selectOption(index)"
          >
            <view class="option-indicator">
              <text class="option-label">{{ getOptionLabel(index) }}</text>
              <u-icon 
                v-if="isOptionSelected(index)"
                :name="currentQuestion.type === 'multiple' ? 'checkbox-mark' : 'checkmark-circle-fill'"
                color="#4A90E2"
                size="32"
              />
            </view>
            <text class="option-text">{{ option }}</text>
          </view>
        </view>
        
        <!-- 判断题选项 -->
        <view v-else-if="currentQuestion.type === 'judge'" class="judge-options">
          <view 
            class="judge-option"
            :class="{ active: currentAnswer === true }"
            @click="selectJudge(true)"
          >
            <u-icon 
              :name="currentAnswer === true ? 'checkmark-circle-fill' : 'checkmark-circle'"
              :color="currentAnswer === true ? '#4CAF50' : '#ccc'"
              size="48"
            />
            <text>正确</text>
          </view>
          <view 
            class="judge-option"
            :class="{ active: currentAnswer === false }"
            @click="selectJudge(false)"
          >
            <u-icon 
              :name="currentAnswer === false ? 'close-circle-fill' : 'close-circle'"
              :color="currentAnswer === false ? '#f56c6c' : '#ccc'"
              size="48"
            />
            <text>错误</text>
          </view>
        </view>
        
        <!-- 问答题输入 -->
        <view v-else-if="currentQuestion.type === 'essay'" class="essay-input">
          <u-textarea 
            v-model="currentAnswer"
            placeholder="请输入您的答案..."
            :maxlength="1000"
            :showWordLimit="true"
            height="400"
            autoHeight
          />
        </view>
      </scroll-view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <u-button 
        v-if="currentQuestionIndex > 0"
        class="prev-btn"
        type="info"
        plain
        @click="prevQuestion"
      >
        上一题
      </u-button>
      
      <u-button 
        class="next-btn"
        type="primary"
        :disabled="!hasAnswer"
        @click="nextQuestion"
      >
        {{ isLastQuestion ? '提交考试' : '下一题' }}
      </u-button>
    </view>
    
    <!-- 答题卡悬浮按钮 -->
    <view class="answer-sheet-fab" @click="showAnswerSheet = true">
      <u-icon name="grid" color="#fff" size="32" />
    </view>
    
    <!-- 答题卡弹窗 -->
    <u-popup 
      v-model="showAnswerSheet" 
      mode="bottom" 
      height="70%"
      :closeOnClickOverlay="true"
    >
      <view class="answer-sheet-modal">
        <view class="modal-header">
          <text class="modal-title">答题卡</text>
          <view class="modal-actions">
            <text class="time-info">剩余时间：{{ formatTime(timeRemaining) }}</text>
            <u-icon name="close" size="32" @click="showAnswerSheet = false" />
          </view>
        </view>
        
        <scroll-view class="answer-grid-container" scroll-y>
          <view class="answer-grid">
            <view 
              v-for="(question, index) in questions" 
              :key="question.id"
              class="answer-item"
              :class="getAnswerItemClass(index)"
              @click="jumpToQuestion(index)"
            >
              <text>{{ index + 1 }}</text>
            </view>
          </view>
        </scroll-view>
        
        <view class="modal-footer">
          <view class="answer-legend">
            <view class="legend-item">
              <view class="legend-color answered"></view>
              <text>已答</text>
            </view>
            <view class="legend-item">
              <view class="legend-color current"></view>
              <text>当前</text>
            </view>
            <view class="legend-item">
              <view class="legend-color unanswered"></view>
              <text>未答</text>
            </view>
          </view>
          
          <u-button 
            class="submit-exam-btn"
            type="warning"
            @click="confirmSubmitExam"
          >
            提交考试
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 提交确认弹窗 -->
    <u-modal 
      v-model="showSubmitModal"
      title="确认提交"
      :content="submitModalContent"
      showCancelButton
      confirmText="确认提交"
      cancelText="继续答题"
      @confirm="submitExam"
      @cancel="showSubmitModal = false"
    />
    
    <!-- 时间警告弹窗 -->
    <u-modal 
      v-model="showTimeWarning"
      title="时间提醒"
      content="考试时间还剩5分钟，请抓紧时间完成答题！"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showTimeWarning = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '../../../src/stores/app'
import { formatDuration } from '../../../src/utils'
import { PAGE_PATHS, EXAM_CONFIG } from '../../../src/constants'
import api from '../../../src/api'
import type { Question, ExamRecord } from '../../../src/types'

// 导入组件
import StatusTag from '../../../src/components/common/StatusTag.vue'

// Store
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  examId: string
  recordId: string
}>()

// 响应式数据
const examTitle = ref('正在考试')
const questions = ref<Question[]>([])
const answers = ref<Record<string, any>>({})
const currentQuestionIndex = ref(0)
const timeRemaining = ref(0)
const examDuration = ref(0)
const showAnswerSheet = ref(false)
const showSubmitModal = ref(false)
const showTimeWarning = ref(false)
const isSubmitting = ref(false)
let timer: number | null = null
let antiCheatLogs: any[] = []

// 计算属性
const totalQuestions = computed(() => questions.value.length)
const currentQuestion = computed(() => questions.value[currentQuestionIndex.value])
const isLastQuestion = computed(() => currentQuestionIndex.value === totalQuestions.value - 1)
const progressPercentage = computed(() => {
  if (totalQuestions.value === 0) return 0
  return ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100
})

const currentAnswer = computed({
  get: () => {
    if (!currentQuestion.value) return null
    return answers.value[currentQuestion.value.id] || null
  },
  set: (value) => {
    if (currentQuestion.value) {
      answers.value[currentQuestion.value.id] = value
    }
  }
})

const hasAnswer = computed(() => {
  if (!currentQuestion.value) return false
  
  const answer = currentAnswer.value
  if (answer === null || answer === undefined) return false
  
  if (currentQuestion.value.type === 'multiple') {
    return Array.isArray(answer) && answer.length > 0
  }
  
  if (currentQuestion.value.type === 'essay') {
    return typeof answer === 'string' && answer.trim().length > 0
  }
  
  return true
})

const isChoiceQuestion = computed(() => {
  return currentQuestion.value?.type === 'single' || currentQuestion.value?.type === 'multiple'
})

const submitModalContent = computed(() => {
  const answeredCount = Object.keys(answers.value).length
  const unansweredCount = totalQuestions.value - answeredCount
  
  if (unansweredCount > 0) {
    return `您还有${unansweredCount}道题未作答，确定要提交考试吗？未作答的题目将按0分计算。`
  } else {
    return '您已完成所有题目，确定要提交考试吗？提交后将无法修改答案。'
  }
})

onMounted(() => {
  loadExamData()
  startTimer()
  setupAntiCheat()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  removeAntiCheat()
})

// 加载考试数据
const loadExamData = async () => {
  try {
    // 获取考试题目
    const response = await api.exam.getExamQuestions(props.examId)
    questions.value = response.data
    
    // 获取考试信息
    const examResponse = await api.exam.getExamDetail(props.examId)
    const exam = examResponse.data
    examTitle.value = exam.name
    examDuration.value = exam.duration * 60 // 转换为秒
    timeRemaining.value = examDuration.value
    
    // 如果是继续考试，加载已有答案
    // const recordResponse = await api.exam.getExamRecord(props.recordId)
    // if (recordResponse.data.answers) {
    //   answers.value = recordResponse.data.answers
    // }
  } catch (error: any) {
    console.error('加载考试数据失败:', error)
    appStore.showToast(error.message || '加载失败')
    appStore.navigateBack()
  }
}

// 开始计时
const startTimer = () => {
  timer = setInterval(() => {
    timeRemaining.value--
    
    // 时间警告
    if (timeRemaining.value === 300 && !showTimeWarning.value) {
      showTimeWarning.value = true
    }
    
    // 自动提交
    if (timeRemaining.value <= EXAM_CONFIG.AUTO_SUBMIT_BEFORE_END) {
      autoSubmitExam()
    }
  }, 1000)
}

// 格式化时间
const formatTime = (seconds: number) => {
  return formatDuration(seconds)
}

// 获取选项标签
const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index)
}

// 获取选项样式类
const getOptionClass = (index: number) => {
  const classes = ['option']
  if (isOptionSelected(index)) {
    classes.push('selected')
  }
  return classes.join(' ')
}

// 检查选项是否被选中
const isOptionSelected = (index: number) => {
  if (!currentQuestion.value) return false
  
  const answer = currentAnswer.value
  if (currentQuestion.value.type === 'multiple') {
    return Array.isArray(answer) && answer.includes(index)
  } else {
    return answer === index
  }
}

// 选择选项
const selectOption = (index: number) => {
  if (!currentQuestion.value) return
  
  if (currentQuestion.value.type === 'multiple') {
    let answer = Array.isArray(currentAnswer.value) ? [...currentAnswer.value] : []
    const selectedIndex = answer.indexOf(index)
    
    if (selectedIndex > -1) {
      answer.splice(selectedIndex, 1)
    } else {
      answer.push(index)
    }
    
    currentAnswer.value = answer
  } else {
    currentAnswer.value = index
  }
}

// 选择判断题答案
const selectJudge = (value: boolean) => {
  currentAnswer.value = value
}

// 上一题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 下一题
const nextQuestion = () => {
  if (isLastQuestion.value) {
    confirmSubmitExam()
  } else {
    currentQuestionIndex.value++
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  currentQuestionIndex.value = index
  showAnswerSheet.value = false
}

// 获取答题卡项目样式类
const getAnswerItemClass = (index: number) => {
  const classes = ['answer-item']
  
  if (index === currentQuestionIndex.value) {
    classes.push('current')
  } else if (answers.value[questions.value[index]?.id]) {
    classes.push('answered')
  } else {
    classes.push('unanswered')
  }
  
  return classes.join(' ')
}

// 确认提交考试
const confirmSubmitExam = () => {
  showSubmitModal.value = true
}

// 提交考试
const submitExam = async () => {
  if (isSubmitting.value) return
  
  isSubmitting.value = true
  showSubmitModal.value = false
  
  try {
    // 停止计时
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    
    // 计算用时
    const duration = examDuration.value - timeRemaining.value
    
    // 提交答案
    await api.exam.submitAnswers({
      recordId: props.recordId,
      answers: answers.value,
      duration,
      antiCheatLogs
    })
    
    appStore.showToast('考试提交成功', 'success')
    
    // 跳转到考试历史页面查看成绩
    setTimeout(() => {
      appStore.redirectTo(PAGE_PATHS.EXAM_HISTORY, { recordId: props.recordId })
    }, 1500)
  } catch (error: any) {
    console.error('提交考试失败:', error)
    appStore.showToast(error.message || '提交失败，请重试')
    
    // 重新开始计时
    startTimer()
  } finally {
    isSubmitting.value = false
  }
}

// 自动提交考试
const autoSubmitExam = () => {
  appStore.showToast('考试时间到，自动提交', 'warning')
  submitExam()
}

// 设置防作弊监控
const setupAntiCheat = () => {
  // 监听页面隐藏事件
  uni.onAppHide(() => {
    logAntiCheat('app_hide', { timestamp: Date.now() })
  })
  
  // 监听页面显示事件
  uni.onAppShow(() => {
    logAntiCheat('app_show', { timestamp: Date.now() })
  })
}

// 移除防作弊监控
const removeAntiCheat = () => {
  // 这里可以移除事件监听器
}

// 记录防作弊日志
const logAntiCheat = (type: string, data: any) => {
  antiCheatLogs.push({
    type,
    data,
    timestamp: Date.now()
  })
  
  // 如果切换应用次数过多，可以考虑警告或强制提交
  const switchCount = antiCheatLogs.filter(log => log.type === 'app_hide').length
  if (switchCount >= EXAM_CONFIG.MAX_SWITCH_COUNT) {
    appStore.showModal({
      title: '违规警告',
      content: '检测到您多次切换应用，这可能被视为作弊行为。请专心答题。',
      showCancelButton: false,
      confirmText: '我知道了'
    })
  }
}
</script>

<style lang="scss" scoped>
@import '../../../src/styles/global.scss';

.exam-answer-container {
  height: 100vh;
  background: $acdc-bg-primary;
  display: flex;
  flex-direction: column;
}

.exam-status-bar {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  
  .status-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .question-progress {
      font-size: 28rpx;
      font-weight: bold;
      color: #4A90E2;
    }
    
    .time-remaining {
      font-size: 28rpx;
      font-weight: bold;
      color: #4CAF50;
      
      &.warning {
        color: #f56c6c;
        animation: blink 1s infinite;
      }
    }
  }
  
  .progress-bar {
    height: 6rpx;
    background: #f0f0f0;
    border-radius: 3rpx;
    overflow: hidden;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
      transition: width 0.3s ease;
    }
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.question-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .question-scroll {
    flex: 1;
    padding: 32rpx;
  }
  
  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    
    .question-info {
      display: flex;
      align-items: center;
      gap: 16rpx;
      
      .question-score {
        font-size: 24rpx;
        color: #FF9500;
        font-weight: bold;
      }
    }
  }
  
  .question-content {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    
    .question-title {
      font-size: 30rpx;
      line-height: 1.6;
      color: #333;
      font-weight: 500;
    }
  }
  
  .question-options {
    .option-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: #fff;
      border: 2rpx solid #f0f0f0;
      border-radius: 16rpx;
      transition: all 0.3s ease;
      
      &.selected {
        border-color: #4A90E2;
        background: rgba(74, 144, 226, 0.05);
      }
      
      .option-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        margin-right: 24rpx;
        
        .option-label {
          font-size: 28rpx;
          font-weight: bold;
          color: #4A90E2;
        }
      }
      
      .option-text {
        flex: 1;
        font-size: 28rpx;
        line-height: 1.5;
        color: #333;
      }
    }
  }
  
  .judge-options {
    display: flex;
    gap: 40rpx;
    justify-content: center;
    
    .judge-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
      padding: 40rpx;
      background: #fff;
      border: 2rpx solid #f0f0f0;
      border-radius: 20rpx;
      flex: 1;
      transition: all 0.3s ease;
      
      &.active {
        border-color: #4A90E2;
        background: rgba(74, 144, 226, 0.05);
      }
      
      text {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
  
  .essay-input {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  }
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 2rpx solid #f0f0f0;
  
  .prev-btn {
    flex: 1;
  }
  
  .next-btn {
    flex: 2;
  }
}

.answer-sheet-fab {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: #4A90E2;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
  z-index: 100;
}

.answer-sheet-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .modal-actions {
      display: flex;
      align-items: center;
      gap: 24rpx;
      
      .time-info {
        font-size: 24rpx;
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
  
  .answer-grid-container {
    flex: 1;
    padding: 40rpx;
  }
  
  .answer-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20rpx;
    
    .answer-item {
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: bold;
      
      &.answered {
        background: #4CAF50;
        color: #fff;
      }
      
      &.current {
        background: #4A90E2;
        color: #fff;
      }
      
      &.unanswered {
        background: #f0f0f0;
        color: #999;
      }
    }
  }
  
  .modal-footer {
    padding: 40rpx;
    border-top: 2rpx solid #f0f0f0;
    
    .answer-legend {
      display: flex;
      justify-content: center;
      gap: 40rpx;
      margin-bottom: 32rpx;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        
        .legend-color {
          width: 24rpx;
          height: 24rpx;
          border-radius: 4rpx;
          
          &.answered {
            background: #4CAF50;
          }
          
          &.current {
            background: #4A90E2;
          }
          
          &.unanswered {
            background: #f0f0f0;
          }
        }
        
        text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
    
    .submit-exam-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 40rpx;
    }
  }
}
</style>
