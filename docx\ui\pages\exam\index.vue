<template>
  <view class="exam-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">考试中心</text>
        <view class="header-actions">
          <u-icon name="calendar" color="#fff" size="44" @click="viewCalendar" />
        </view>
      </view>
    </view>
    
    <!-- 非正式用户状态提示 -->
    <view v-if="!userStore.isVerified" class="auth-prompt">
      <view class="prompt-card">
        <u-icon name="lock" color="#f56c6c" size="80" />
        <text class="prompt-title">未认证，无法考试</text>
        <text class="prompt-desc">请先完善个人资料并通过机构审核，才能参加考试</text>
        <u-button 
          class="auth-btn" 
          type="primary" 
          size="medium"
          @click="goToAuth"
        >
          去完善资料
        </u-button>
      </view>
    </view>
    
    <!-- 正式用户考试内容 -->
    <view v-else class="main-content">
      <!-- 考试统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ examStats.total }}</text>
          <text class="stats-label">参加考试</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ examStats.passed }}</text>
          <text class="stats-label">通过考试</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ examStats.pending }}</text>
          <text class="stats-label">待参加</text>
        </view>
      </view>
      
      <!-- 本期考试 -->
      <view class="current-exams-section">
        <view class="section-header">
          <text class="section-title">本期考试</text>
          <text class="section-desc">请及时参加考试，不要错过考试时间</text>
        </view>
        
        <view v-if="currentExams.length === 0" class="empty-exams">
          <u-empty 
            text="暂无待参加的考试" 
            icon="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=200&h=200&fit=crop&crop=center"
            textSize="28"
          />
        </view>
        
        <view v-else class="exam-list">
          <view 
            v-for="exam in currentExams" 
            :key="exam.id"
            class="exam-card"
            @click="handleExamClick(exam)"
          >
            <!-- 考试类型标签 -->
            <view class="exam-type-tag" :class="exam.type">
              <text>{{ exam.type === 'online' ? '线上考试' : '线下考试' }}</text>
            </view>
            
            <!-- 考试信息 -->
            <view class="exam-info">
              <text class="exam-title">{{ exam.title }}</text>
              <text class="exam-desc">{{ exam.description }}</text>
              
              <view class="exam-meta">
                <view class="meta-item">
                  <u-icon name="clock" color="#666" size="24" />
                  <text>{{ formatExamTime(exam) }}</text>
                </view>
                <view class="meta-item">
                  <u-icon name="account" color="#666" size="24" />
                  <text>{{ exam.duration }}分钟</text>
                </view>
              </view>
            </view>
            
            <!-- 考试状态和操作 -->
            <view class="exam-actions">
              <view class="exam-status" :class="exam.status">
                <text>{{ getStatusText(exam.status) }}</text>
              </view>
              
              <u-button 
                class="action-btn"
                :type="getButtonType(exam.status)"
                size="small"
                :disabled="!canAction(exam)"
              >
                {{ getActionText(exam) }}
              </u-button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 历史考试记录入口 -->
      <view class="history-section">
        <view class="history-card" @click="viewHistory">
          <view class="history-info">
            <u-icon name="file-text" color="#4A90E2" size="60" />
            <view class="history-content">
              <text class="history-title">历史考试记录</text>
              <text class="history-desc">查看所有考试记录和成绩</text>
            </view>
          </view>
          <u-icon name="arrow-right" color="#c0c4cc" size="32" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

// Store
const userStore = useUserStore()

// 系统信息
const statusBarHeight = ref(0)

// 考试统计数据
const examStats = ref({
  total: 12,
  passed: 10,
  pending: 2
})

// 当前考试列表
const currentExams = ref([
  {
    id: 1,
    title: '2024年第二季度疾控医护任职资格考试',
    description: '包含疫苗接种、预防保健、公共卫生等内容',
    type: 'online', // online | offline
    status: 'not_started', // not_started | in_progress | completed | expired
    startTime: '2024-04-15 09:00:00',
    endTime: '2024-04-15 11:00:00',
    duration: 120,
    venue: null
  },
  {
    id: 2,
    title: '犬伤门诊专项技能考试',
    description: '犬伤处置流程及疫苗接种专项考试',
    type: 'offline',
    status: 'not_registered', // not_registered | registered | completed
    startTime: '2024-04-20 14:00:00',
    endTime: '2024-04-20 16:00:00',
    duration: 120,
    venue: '市疾控中心3楼考试室'
  },
  {
    id: 3,
    title: '产科技能考核',
    description: '产科相关疫苗接种及健康管理考核',
    type: 'online',
    status: 'in_progress',
    startTime: '2024-03-15 09:00:00',
    endTime: '2024-03-15 11:00:00',
    duration: 90,
    venue: null
  }
])

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 加载考试数据
  loadExamData()
})

// 去认证
const goToAuth = () => {
  uni.navigateTo({
    url: '/pages/profile/submit'
  })
}

// 查看日历
const viewCalendar = () => {
  uni.showToast({
    title: '考试日历功能开发中',
    icon: 'none'
  })
}

// 格式化考试时间
const formatExamTime = (exam: any) => {
  const start = new Date(exam.startTime)
  const end = new Date(exam.endTime)
  
  if (exam.type === 'online') {
    return `${start.getMonth() + 1}月${start.getDate()}日 ${start.getHours().toString().padStart(2, '0')}:${start.getMinutes().toString().padStart(2, '0')}-${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`
  } else {
    return `${start.getMonth() + 1}月${start.getDate()}日 ${start.getHours().toString().padStart(2, '0')}:${start.getMinutes().toString().padStart(2, '0')}`
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'not_started': '未开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'expired': '已过期',
    'not_registered': '未报名',
    'registered': '已报名'
  }
  return statusMap[status] || '未知状态'
}

// 获取按钮类型
const getButtonType = (status: string) => {
  const typeMap = {
    'not_started': 'primary',
    'in_progress': 'warning',
    'completed': 'info',
    'expired': 'info',
    'not_registered': 'primary',
    'registered': 'success'
  }
  return typeMap[status] || 'default'
}

// 是否可以操作
const canAction = (exam: any) => {
  return !['completed', 'expired'].includes(exam.status)
}

// 获取操作文本
const getActionText = (exam: any) => {
  const actionMap = {
    'not_started': '准备考试',
    'in_progress': '继续考试',
    'completed': '查看成绩',
    'expired': '已过期',
    'not_registered': '立即报名',
    'registered': '查看详情'
  }
  return actionMap[exam.status] || '查看详情'
}

// 处理考试点击
const handleExamClick = (exam: any) => {
  if (exam.type === 'online') {
    handleOnlineExam(exam)
  } else {
    handleOfflineExam(exam)
  }
}

// 处理线上考试
const handleOnlineExam = (exam: any) => {
  if (exam.status === 'not_started') {
    // 跳转到考前阅读
    uni.navigateTo({
      url: `/pages/exam/online-prepare?examId=${exam.id}`
    })
  } else if (exam.status === 'in_progress') {
    // 继续考试
    uni.showModal({
      title: '继续考试',
      content: '检测到您有未完成的考试，是否继续？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: `/pages/exam/online-exam?examId=${exam.id}`
          })
        }
      }
    })
  } else if (exam.status === 'completed') {
    // 查看成绩
    uni.navigateTo({
      url: `/pages/exam/result?examId=${exam.id}`
    })
  }
}

// 处理线下考试
const handleOfflineExam = (exam: any) => {
  if (exam.status === 'not_registered') {
    // 跳转到报名页面
    uni.navigateTo({
      url: `/pages/exam/offline-register?examId=${exam.id}`
    })
  } else if (exam.status === 'registered') {
    // 查看报名详情
    uni.navigateTo({
      url: `/pages/exam/offline-detail?examId=${exam.id}`
    })
  }
}

// 查看历史记录
const viewHistory = () => {
  uni.navigateTo({
    url: '/pages/exam/history'
  })
}

// 加载考试数据
const loadExamData = async () => {
  try {
    // 这里应该调用真实的API获取考试数据
    // const response = await getExamList()
    // currentExams.value = response.data
    
    console.log('考试数据加载完成')
  } catch (error) {
    console.error('加载考试数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.exam-center-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
}

.auth-prompt {
  padding: 60rpx 30rpx;
  
  .prompt-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 80rpx 40rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    
    .prompt-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin: 40rpx 0 20rpx;
    }
    
    .prompt-desc {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 60rpx;
    }
    
    .auth-btn {
      width: 300rpx;
      height: 80rpx;
      border-radius: 40rpx;
    }
  }
}

.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx; // 为底部导航留空间
}

.stats-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  
  .stats-item {
    flex: 1;
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #4A90E2;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .stats-divider {
    width: 2rpx;
    height: 60rpx;
    background: #f0f0f0;
  }
}

.current-exams-section {
  margin-bottom: 40rpx;
  
  .section-header {
    margin-bottom: 30rpx;
    
    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .section-desc {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .empty-exams {
    padding: 80rpx 0;
  }
  
  .exam-list {
    .exam-card {
      position: relative;
      background: #fff;
      border-radius: 24rpx;
      padding: 40rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      
      .exam-type-tag {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
        font-size: 20rpx;
        color: #fff;
        
        &.online {
          background: #4A90E2;
        }
        
        &.offline {
          background: #FF9500;
        }
      }
      
      .exam-info {
        margin-bottom: 30rpx;
        padding-right: 100rpx; // 为标签留空间
        
        .exam-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }
        
        .exam-desc {
          display: block;
          font-size: 26rpx;
          color: #666;
          margin-bottom: 20rpx;
          line-height: 1.4;
        }
        
        .exam-meta {
          display: flex;
          gap: 40rpx;
          
          .meta-item {
            display: flex;
            align-items: center;
            
            text {
              margin-left: 8rpx;
              font-size: 24rpx;
              color: #666;
            }
          }
        }
      }
      
      .exam-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .exam-status {
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
          font-size: 24rpx;
          
          &.not_started {
            background: #e3f2fd;
            color: #1976d2;
          }
          
          &.in_progress {
            background: #fff3e0;
            color: #f57c00;
          }
          
          &.completed {
            background: #e8f5e8;
            color: #388e3c;
          }
          
          &.expired {
            background: #fafafa;
            color: #9e9e9e;
          }
          
          &.not_registered {
            background: #ffebee;
            color: #d32f2f;
          }
          
          &.registered {
            background: #e8f5e8;
            color: #388e3c;
          }
        }
        
        .action-btn {
          width: 160rpx;
          height: 64rpx;
          border-radius: 32rpx;
        }
      }
    }
  }
}

.history-section {
  .history-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .history-info {
      display: flex;
      align-items: center;
      flex: 1;
      
      .history-content {
        margin-left: 30rpx;
        
        .history-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .history-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}
</style> 