import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { QuestionCategory, Question, PracticeSession } from '../types'
import { STORAGE_KEYS, PRACTICE_CONFIG } from '../constants'

export const useStudyStore = defineStore('study', () => {
  // 状态
  const categories = ref<QuestionCategory[]>([])
  const currentSession = ref<PracticeSession | null>(null)
  const practiceHistory = ref<PracticeSession[]>([])
  const dailyPracticeCount = ref<number>(0)
  const lastPracticeDate = ref<string>('')

  // 计算属性
  const canPracticeToday = computed(() => {
    const today = new Date().toDateString()
    if (lastPracticeDate.value !== today) {
      return true // 新的一天，重置次数
    }
    return dailyPracticeCount.value < PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY
  })

  const remainingPracticeCount = computed(() => {
    const today = new Date().toDateString()
    if (lastPracticeDate.value !== today) {
      return PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY // 新的一天
    }
    return Math.max(0, PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY - dailyPracticeCount.value)
  })

  const currentQuestionIndex = computed(() => {
    if (!currentSession.value) return 0
    return Object.keys(currentSession.value.answers).length
  })

  const currentQuestion = computed(() => {
    if (!currentSession.value || currentQuestionIndex.value >= currentSession.value.questions.length) {
      return null
    }
    return currentSession.value.questions[currentQuestionIndex.value]
  })

  const isSessionCompleted = computed(() => {
    if (!currentSession.value) return false
    return currentQuestionIndex.value >= currentSession.value.questions.length
  })

  // 方法
  const setCategories = (newCategories: QuestionCategory[]) => {
    categories.value = newCategories
  }

  const startPracticeSession = (categoryId: string, questions: Question[]) => {
    const session: PracticeSession = {
      id: Date.now().toString(),
      categoryId,
      questions,
      answers: {},
      totalCount: questions.length,
      startTime: new Date().toISOString(),
    }
    
    currentSession.value = session
    saveCurrentSession()
    return session
  }

  const answerQuestion = (questionId: string, answer: any) => {
    if (!currentSession.value) return

    currentSession.value.answers[questionId] = answer
    saveCurrentSession()
  }

  const completeSession = () => {
    if (!currentSession.value || !isSessionCompleted.value) return

    // 计算得分
    let correctCount = 0
    currentSession.value.questions.forEach(question => {
      const userAnswer = currentSession.value!.answers[question.id]
      if (isAnswerCorrect(question, userAnswer)) {
        correctCount++
      }
    })

    currentSession.value.correctCount = correctCount
    currentSession.value.score = Math.round((correctCount / currentSession.value.totalCount) * 100)
    currentSession.value.endTime = new Date().toISOString()

    // 添加到历史记录
    practiceHistory.value.unshift({ ...currentSession.value })
    
    // 更新每日练习次数
    updateDailyPracticeCount()
    
    // 保存数据
    savePracticeHistory()
    saveCurrentSession()

    return currentSession.value
  }

  const isAnswerCorrect = (question: Question, userAnswer: any): boolean => {
    if (!userAnswer) return false

    switch (question.type) {
      case 'single':
      case 'judge':
        return userAnswer === question.answer
      case 'multiple':
        if (!Array.isArray(userAnswer) || !Array.isArray(question.answer)) return false
        return userAnswer.length === question.answer.length && 
               userAnswer.every(ans => question.answer.includes(ans))
      case 'essay':
        // 问答题需要人工评判，这里暂时返回true
        return true
      default:
        return false
    }
  }

  const updateDailyPracticeCount = () => {
    const today = new Date().toDateString()
    
    if (lastPracticeDate.value !== today) {
      // 新的一天，重置计数
      dailyPracticeCount.value = 1
      lastPracticeDate.value = today
    } else {
      // 同一天，增加计数
      dailyPracticeCount.value++
    }
    
    saveDailyPracticeData()
  }

  const clearCurrentSession = () => {
    currentSession.value = null
    uni.removeStorageSync(STORAGE_KEYS.PRACTICE_CACHE)
  }

  const saveCurrentSession = () => {
    if (currentSession.value) {
      try {
        uni.setStorageSync(STORAGE_KEYS.PRACTICE_CACHE, JSON.stringify(currentSession.value))
      } catch (error) {
        console.error('Failed to save current session:', error)
      }
    }
  }

  const loadCurrentSession = () => {
    try {
      const cached = uni.getStorageSync(STORAGE_KEYS.PRACTICE_CACHE)
      if (cached) {
        currentSession.value = JSON.parse(cached)
      }
    } catch (error) {
      console.error('Failed to load current session:', error)
    }
  }

  const savePracticeHistory = () => {
    try {
      // 只保存最近50条记录
      const historyToSave = practiceHistory.value.slice(0, 50)
      uni.setStorageSync('practice_history', JSON.stringify(historyToSave))
    } catch (error) {
      console.error('Failed to save practice history:', error)
    }
  }

  const loadPracticeHistory = () => {
    try {
      const history = uni.getStorageSync('practice_history')
      if (history) {
        practiceHistory.value = JSON.parse(history)
      }
    } catch (error) {
      console.error('Failed to load practice history:', error)
    }
  }

  const saveDailyPracticeData = () => {
    try {
      const data = {
        count: dailyPracticeCount.value,
        date: lastPracticeDate.value,
      }
      uni.setStorageSync('daily_practice', JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save daily practice data:', error)
    }
  }

  const loadDailyPracticeData = () => {
    try {
      const data = uni.getStorageSync('daily_practice')
      if (data) {
        const parsed = JSON.parse(data)
        dailyPracticeCount.value = parsed.count || 0
        lastPracticeDate.value = parsed.date || ''
      }
    } catch (error) {
      console.error('Failed to load daily practice data:', error)
    }
  }

  const initStudyStore = () => {
    loadCurrentSession()
    loadPracticeHistory()
    loadDailyPracticeData()
  }

  const getSessionStats = () => {
    const totalSessions = practiceHistory.value.length
    const totalQuestions = practiceHistory.value.reduce((sum, session) => sum + session.totalCount, 0)
    const totalCorrect = practiceHistory.value.reduce((sum, session) => sum + (session.correctCount || 0), 0)
    const averageScore = totalSessions > 0 ? 
      practiceHistory.value.reduce((sum, session) => sum + (session.score || 0), 0) / totalSessions : 0

    return {
      totalSessions,
      totalQuestions,
      totalCorrect,
      averageScore: Math.round(averageScore),
      accuracy: totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0,
    }
  }

  return {
    // 状态
    categories,
    currentSession,
    practiceHistory,
    dailyPracticeCount,
    lastPracticeDate,
    
    // 计算属性
    canPracticeToday,
    remainingPracticeCount,
    currentQuestionIndex,
    currentQuestion,
    isSessionCompleted,
    
    // 方法
    setCategories,
    startPracticeSession,
    answerQuestion,
    completeSession,
    isAnswerCorrect,
    updateDailyPracticeCount,
    clearCurrentSession,
    saveCurrentSession,
    loadCurrentSession,
    savePracticeHistory,
    loadPracticeHistory,
    saveDailyPracticeData,
    loadDailyPracticeData,
    initStudyStore,
    getSessionStats,
  }
})
