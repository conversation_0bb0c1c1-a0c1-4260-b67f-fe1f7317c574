import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/constants': resolve(__dirname, 'src/constants'),
      '@/styles': resolve(__dirname, 'src/styles'),
      'uni_modules': resolve(__dirname, 'uni_modules')
    }
  },
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    open: false
  },
  build: {
    target: 'es6',
    cssTarget: 'chrome61',
    rollupOptions: {
      output: {
        manualChunks: {
          'uni-modules': ['@dcloudio/uni-app'],
          'vue-vendor': ['vue', 'pinia'],
          'ui-vendor': ['uview-plus']
        }
      }
    }
  },
  optimizeDeps: {
    include: [
      'vue',
      'pinia',
      '@dcloudio/uni-app'
    ]
  }
})
