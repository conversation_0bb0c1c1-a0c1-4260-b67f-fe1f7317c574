<template>
  <view class="reading-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="考前须知" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 考试信息卡片 -->
    <view v-if="examInfo" class="exam-info-card">
      <view class="exam-header">
        <text class="exam-title">{{ examInfo.name }}</text>
        <view class="exam-type-tag online">
          <text>线上考试</text>
        </view>
      </view>
      
      <view class="exam-details">
        <view class="detail-row">
          <u-icon name="clock" color="#4A90E2" size="32" />
          <text class="detail-label">考试时长：</text>
          <text class="detail-value">{{ examInfo.duration }}分钟</text>
        </view>
        <view class="detail-row">
          <u-icon name="file-text" color="#4A90E2" size="32" />
          <text class="detail-label">总分：</text>
          <text class="detail-value">{{ examInfo.totalScore }}分</text>
        </view>
        <view class="detail-row">
          <u-icon name="checkmark-circle" color="#4A90E2" size="32" />
          <text class="detail-label">及格分：</text>
          <text class="detail-value">{{ examInfo.passScore }}分</text>
        </view>
      </view>
    </view>
    
    <!-- 考试须知内容 -->
    <view class="reading-content">
      <view class="content-section">
        <view class="section-header">
          <u-icon name="warning" color="#FF9500" size="32" />
          <text class="section-title">重要提醒</text>
        </view>
        <view class="notice-list">
          <view class="notice-item important">
            <u-icon name="exclamation-circle" color="#f56c6c" size="24" />
            <text>考试过程中请保持网络连接稳定，避免因网络问题影响考试</text>
          </view>
          <view class="notice-item important">
            <u-icon name="exclamation-circle" color="#f56c6c" size="24" />
            <text>考试开始后不允许切换应用或退出考试界面，否则将被视为作弊</text>
          </view>
          <view class="notice-item important">
            <u-icon name="exclamation-circle" color="#f56c6c" size="24" />
            <text>请确保设备电量充足，建议在考试前充电至80%以上</text>
          </view>
        </view>
      </view>
      
      <view class="content-section">
        <view class="section-header">
          <u-icon name="shield" color="#4A90E2" size="32" />
          <text class="section-title">身份验证要求</text>
        </view>
        <view class="auth-requirements">
          <view class="requirement-item">
            <view class="requirement-icon">
              <u-icon name="camera" color="#4A90E2" size="40" />
            </view>
            <view class="requirement-content">
              <text class="requirement-title">人脸识别验证</text>
              <text class="requirement-desc">考试前需要进行人脸识别验证，请确保光线充足，面部清晰可见</text>
            </view>
          </view>
          <view class="requirement-item">
            <view class="requirement-icon">
              <u-icon name="account" color="#4A90E2" size="40" />
            </view>
            <view class="requirement-content">
              <text class="requirement-title">本人参考</text>
              <text class="requirement-desc">必须本人参加考试，不得代考或使用他人身份信息</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="content-section">
        <view class="section-header">
          <u-icon name="list" color="#4A90E2" size="32" />
          <text class="section-title">考试规则</text>
        </view>
        <view class="rules-list">
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">考试时间到达后系统将自动提交答案，请合理安排答题时间</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">每道题目只能作答一次，提交后不可修改，请仔细检查后再提交</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">考试过程中如遇技术问题，请及时联系技术支持</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">4</text>
            <text class="rule-text">考试结束后可立即查看成绩，证书将在审核通过后发放</text>
          </view>
        </view>
      </view>
      
      <view class="content-section">
        <view class="section-header">
          <u-icon name="settings" color="#4A90E2" size="32" />
          <text class="section-title">设备要求</text>
        </view>
        <view class="device-requirements">
          <view class="device-item">
            <u-icon name="wifi" color="#4CAF50" size="32" />
            <view class="device-content">
              <text class="device-title">网络连接</text>
              <text class="device-desc">稳定的WiFi或4G网络</text>
            </view>
            <view class="device-status" :class="{ good: networkStatus }">
              <text>{{ networkStatus ? '正常' : '异常' }}</text>
            </view>
          </view>
          <view class="device-item">
            <u-icon name="battery-charging" color="#4CAF50" size="32" />
            <view class="device-content">
              <text class="device-title">设备电量</text>
              <text class="device-desc">建议80%以上电量</text>
            </view>
            <view class="device-status" :class="{ good: batteryLevel >= 80 }">
              <text>{{ batteryLevel }}%</text>
            </view>
          </view>
          <view class="device-item">
            <u-icon name="camera" color="#4CAF50" size="32" />
            <view class="device-content">
              <text class="device-title">摄像头权限</text>
              <text class="device-desc">用于人脸识别验证</text>
            </view>
            <view class="device-status" :class="{ good: cameraPermission }">
              <text>{{ cameraPermission ? '已授权' : '未授权' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 确认按钮 -->
    <view class="confirm-section">
      <view class="confirm-checkbox">
        <u-checkbox 
          v-model="hasRead" 
          :customStyle="{ marginRight: '16rpx' }"
          activeColor="#4A90E2"
        />
        <text class="checkbox-text">我已仔细阅读并同意遵守以上考试规则和要求</text>
      </view>
      
      <u-button 
        class="start-exam-btn"
        type="primary"
        :disabled="!hasRead || !canStartExam"
        :loading="isStarting"
        loadingText="准备中..."
        @click="startExam"
      >
        开始考试
      </u-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '../../../src/stores/app'
import { PAGE_PATHS } from '../../../src/constants'
import api from '../../../src/api'
import type { Exam } from '../../../src/types'

// Store
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  examId: string
}>()

// 响应式数据
const examInfo = ref<Exam | null>(null)
const hasRead = ref(false)
const isStarting = ref(false)
const networkStatus = ref(true)
const batteryLevel = ref(100)
const cameraPermission = ref(false)

// 计算属性
const canStartExam = computed(() => {
  return networkStatus.value && batteryLevel.value >= 20 && cameraPermission.value
})

onMounted(() => {
  loadExamInfo()
  checkDeviceStatus()
})

// 加载考试信息
const loadExamInfo = async () => {
  try {
    const response = await api.exam.getExamDetail(props.examId)
    examInfo.value = response.data
  } catch (error: any) {
    console.error('加载考试信息失败:', error)
    appStore.showToast(error.message || '加载失败')
    appStore.navigateBack()
  }
}

// 检查设备状态
const checkDeviceStatus = async () => {
  try {
    // 检查网络状态
    const networkInfo = await uni.getNetworkType()
    networkStatus.value = networkInfo[1].networkType !== 'none'
    
    // 检查电池状态
    const batteryInfo = await uni.getBatteryInfo()
    batteryLevel.value = batteryInfo[1].level
    
    // 检查摄像头权限
    const authResult = await uni.authorize({
      scope: 'scope.camera'
    })
    cameraPermission.value = true
  } catch (error) {
    console.error('设备状态检查失败:', error)
    // 尝试获取摄像头权限
    try {
      await uni.authorize({
        scope: 'scope.camera'
      })
      cameraPermission.value = true
    } catch (authError) {
      cameraPermission.value = false
      appStore.showModal({
        title: '需要摄像头权限',
        content: '考试需要使用摄像头进行人脸识别验证，请在设置中开启摄像头权限。',
        confirmText: '去设置',
        cancelText: '取消'
      }).then((confirmed) => {
        if (confirmed) {
          uni.openSetting()
        }
      })
    }
  }
}

// 开始考试
const startExam = async () => {
  if (!hasRead.value) {
    appStore.showToast('请先阅读并同意考试规则')
    return
  }
  
  if (!canStartExam.value) {
    appStore.showToast('设备状态不满足考试要求')
    return
  }
  
  isStarting.value = true
  
  try {
    // 检查考试状态
    const examDetail = await api.exam.getExamDetail(props.examId)
    const exam = examDetail.data
    
    if (exam.status !== 'not_started' && exam.status !== 'in_progress') {
      appStore.showToast('考试已结束或不在考试时间内')
      appStore.navigateBack()
      return
    }
    
    // 跳转到人脸识别验证页面
    appStore.redirectTo(PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY, { 
      examId: props.examId 
    })
  } catch (error: any) {
    console.error('开始考试失败:', error)
    appStore.showToast(error.message || '开始考试失败，请重试')
  } finally {
    isStarting.value = false
  }
}
</script>

<style lang="scss" scoped>
@import '../../../src/styles/global.scss';

.reading-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 200rpx;
}

.exam-info-card {
  margin: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .exam-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    
    .exam-title {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .exam-type-tag {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 20rpx;
      color: #fff;
      
      &.online {
        background: #4A90E2;
      }
    }
  }
  
  .exam-details {
    .detail-row {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .detail-label {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #666;
      }
      
      .detail-value {
        margin-left: 8rpx;
        font-size: 26rpx;
        font-weight: bold;
        color: #4A90E2;
      }
    }
  }
}

.reading-content {
  margin: 24rpx;
  
  .content-section {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;
      
      .section-title {
        margin-left: 16rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

.notice-list {
  .notice-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    margin-bottom: 16rpx;
    border-radius: 12rpx;
    
    &.important {
      background: #fff7e6;
      border: 2rpx solid #ffd591;
    }
    
    text {
      flex: 1;
      margin-left: 16rpx;
      font-size: 26rpx;
      line-height: 1.5;
      color: #333;
    }
  }
}

.auth-requirements {
  .requirement-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .requirement-icon {
      width: 80rpx;
      height: 80rpx;
      background: rgba(74, 144, 226, 0.1);
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
    }
    
    .requirement-content {
      flex: 1;
      
      .requirement-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .requirement-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

.rules-list {
  .rule-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .rule-number {
      width: 40rpx;
      height: 40rpx;
      background: #4A90E2;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: bold;
      color: #fff;
      margin-right: 20rpx;
      flex-shrink: 0;
    }
    
    .rule-text {
      flex: 1;
      font-size: 26rpx;
      line-height: 1.5;
      color: #333;
    }
  }
}

.device-requirements {
  .device-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .device-content {
      flex: 1;
      margin-left: 20rpx;
      
      .device-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 4rpx;
      }
      
      .device-desc {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .device-status {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 24rpx;
      background: #fff2f0;
      color: #f56c6c;
      
      &.good {
        background: #f6ffed;
        color: #4CAF50;
      }
    }
  }
}

.confirm-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .confirm-checkbox {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32rpx;
    
    .checkbox-text {
      flex: 1;
      font-size: 26rpx;
      line-height: 1.5;
      color: #333;
    }
  }
  
  .start-exam-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
