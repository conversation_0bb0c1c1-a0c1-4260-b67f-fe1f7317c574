# 疾控医护考试系统 - 项目文件结构说明

## 📋 项目概述

疾控医护考试系统是一个基于 uni-app 开发的跨平台移动应用，采用 Vue3 + TypeScript + uview-plus + Pinia 技术栈，为疾控医护人员提供学习、练习和考试功能。

## 🏗️ 项目架构

### 技术栈
- **框架**: uni-app (Vue3 + TypeScript)
- **UI组件库**: uview-plus
- **状态管理**: Pinia
- **样式预处理**: SCSS
- **构建工具**: HBuilderX / Vite

### 架构模式
- **主包+分包**: 优化小程序包体积
- **组件化开发**: 可复用的公共组件
- **模块化设计**: 清晰的业务模块划分

## 📁 目录结构详解

```
疾控医护考试系统/
├── 📁 pages/                    # 主包页面（底部导航页面）
│   ├── 📁 login/               # 登录模块
│   │   └── login.vue          # 登录页面
│   ├── 📁 info/               # 信息中心模块
│   │   ├── info.vue           # 信息中心主页
│   │   └── detail.vue         # 公告详情页
│   ├── 📁 study/              # 学习中心模块
│   │   ├── study.vue          # 学习中心主页
│   │   ├── category.vue       # 题库分类选择
│   │   ├── practice.vue       # 题库练习页面
│   │   └── summary.vue        # 练习总结页面
│   ├── 📁 exam/               # 考试中心模块
│   │   └── exam.vue           # 考试中心主页
│   ├── 📁 personal/           # 个人中心模块
│   │   └── personal.vue       # 个人中心主页
│   └── 📁 profile/            # 个人资料模块
│       └── profile.vue        # 个人资料提交页
│
├── 📁 subpages/                # 分包页面
│   ├── 📁 exam/               # 考试相关分包
│   │   ├── 📁 online/         # 线上考试
│   │   │   ├── reading.vue    # 考前阅读页面
│   │   │   ├── face-verify.vue # 人脸识别验证
│   │   │   └── answer.vue     # 在线答题页面
│   │   ├── 📁 offline/        # 线下考试（预留）
│   │   └── 📁 history/        # 考试历史（预留）
│   └── 📁 personal/           # 个人中心分包（预留）
│       ├── 📁 info/           # 个人信息管理
│       ├── 📁 certificate/    # 证书管理
│       ├── 📁 feedback/       # 意见反馈
│       └── 📁 about/          # 关于我们
│
├── 📁 src/                     # 源码目录
│   ├── 📁 api/                # API接口
│   │   └── index.ts           # API统一导出
│   ├── 📁 components/         # 公共组件
│   │   └── 📁 common/         # 通用组件
│   │       ├── EmptyState.vue        # 空状态组件
│   │       ├── LoadingSpinner.vue    # 加载组件
│   │       ├── PermissionWrapper.vue # 权限包装组件
│   │       ├── StatusTag.vue         # 状态标签组件
│   │       └── UserStatusBanner.vue  # 用户状态横幅
│   ├── 📁 stores/             # Pinia状态管理
│   │   ├── app.ts             # 应用全局状态
│   │   ├── user.ts            # 用户状态管理
│   │   └── study.ts           # 学习状态管理
│   ├── 📁 utils/              # 工具函数
│   │   ├── index.ts           # 通用工具函数
│   │   ├── request.ts         # 网络请求封装
│   │   ├── permission.ts      # 权限管理工具
│   │   └── router-guard.ts    # 路由守卫
│   ├── 📁 types/              # TypeScript类型定义
│   │   └── index.ts           # 类型定义文件
│   ├── 📁 constants/          # 常量定义
│   │   └── index.ts           # 常量配置
│   └── 📁 styles/             # 样式文件
│       └── global.scss        # 全局样式
│
├── 📁 static/                  # 静态资源
│   ├── 📁 images/             # 图片资源
│   ├── 📁 icons/              # 图标资源
│   └── 📁 font/               # 字体资源
│
├── 📁 uni_modules/            # uni-app插件模块
│   ├── 📁 uview-plus/         # uview-plus UI组件库
│   └── 📁 uni-*/              # 其他uni-app官方组件
│
├── 📁 docx/                   # 项目文档
│   ├── 📁 ui/                 # UI设计文档
│   └── 产品需求文档.md         # 产品需求文档
│
├── 📄 App.vue                 # 应用入口文件
├── 📄 main.js                 # 应用启动文件
├── 📄 pages.json              # 页面配置文件
├── 📄 manifest.json           # 应用配置文件
├── 📄 uni.scss                # uni-app全局样式
├── 📄 tsconfig.json           # TypeScript配置
├── 📄 package.json            # 项目依赖配置
└── 📄 README.md               # 项目说明文档
```

## 🎯 核心功能模块

### 1. 用户认证模块 (`pages/login/`, `pages/profile/`)
- **登录页面**: 微信授权登录
- **个人资料**: 用户信息提交和审核

### 2. 信息中心模块 (`pages/info/`)
- **信息主页**: 公告列表和分类筛选
- **详情页面**: 公告详细内容查看

### 3. 学习中心模块 (`pages/study/`)
- **学习主页**: 学习统计和模块入口
- **题库分类**: 选择练习分类
- **题库练习**: 多题型练习功能
- **练习总结**: 成绩分析和学习建议

### 4. 考试中心模块 (`pages/exam/`, `subpages/exam/`)
- **考试主页**: 考试列表和状态管理
- **考前阅读**: 考试须知和设备检查
- **人脸识别**: 身份验证流程
- **在线答题**: 考试答题和防作弊

### 5. 个人中心模块 (`pages/personal/`)
- **个人主页**: 用户信息和统计数据
- **功能菜单**: 各种设置和管理入口

## 🔧 技术特色

### 权限控制系统
- **PermissionWrapper组件**: 统一的权限控制包装器
- **路由守卫**: 页面级权限验证
- **功能权限**: 基于用户状态的功能控制

### 状态管理
- **用户状态**: 登录状态、个人信息、权限等级
- **应用状态**: 全局配置、导航状态、弹窗管理
- **学习状态**: 练习记录、学习统计、题库数据

### 组件化开发
- **公共组件**: 可复用的UI组件
- **业务组件**: 特定业务场景的组件
- **工具组件**: 功能性辅助组件

### 样式规范
- **全局样式**: 统一的设计规范和变量
- **主题色彩**: 疾控特色的海绿色系
- **响应式设计**: 适配不同屏幕尺寸

## 📱 页面路由配置

### 主包页面 (pages.json)
```json
{
  "pages": [
    { "path": "pages/login/login" },
    { "path": "pages/info/info" },
    { "path": "pages/study/study" },
    { "path": "pages/exam/exam" },
    { "path": "pages/personal/personal" }
  ],
  "tabBar": {
    "list": [
      { "pagePath": "pages/info/info", "text": "信息" },
      { "pagePath": "pages/study/study", "text": "学习" },
      { "pagePath": "pages/exam/exam", "text": "考试" },
      { "pagePath": "pages/personal/personal", "text": "我的" }
    ]
  }
}
```

### 分包配置
- **exam分包**: 考试相关页面，按需加载
- **personal分包**: 个人中心相关页面，按需加载

## 🛠️ 开发规范

### 代码规范
- **TypeScript**: 强类型约束，提高代码质量
- **ESLint**: 代码风格统一
- **组件命名**: PascalCase命名规范
- **文件命名**: kebab-case命名规范

### 项目规范
- **模块化**: 按功能模块组织代码
- **组件化**: 可复用的组件设计
- **类型安全**: 完整的TypeScript类型定义
- **样式规范**: SCSS + BEM命名规范

## 🚀 部署说明

### 开发环境
1. 安装依赖: `npm install`
2. 启动开发: `npm run dev:mp-weixin`
3. 使用微信开发者工具打开 `dist/dev/mp-weixin`

### 生产环境
1. 构建项目: `npm run build:mp-weixin`
2. 上传代码到微信小程序后台
3. 提交审核和发布

## 📊 功能特性详解

### 用户认证系统
- **微信授权登录**: 一键登录，获取用户基本信息
- **个人资料提交**: 完善职业信息，支持照片上传
- **审核流程**: 机构审核，状态实时更新
- **权限分级**: 基于审核状态的功能权限控制

### 学习功能
- **题库练习**: 支持单选、多选、判断、问答四种题型
- **分类练习**: 按专业领域分类的题库
- **学习统计**: 练习次数、正确率、学习时长统计
- **即时反馈**: 答题后立即显示正确答案和解析

### 考试功能
- **线上考试**: 完整的在线考试流程
- **人脸识别**: 考前身份验证，防止代考
- **防作弊监控**: 应用切换检测，异常行为记录
- **自动提交**: 时间到自动提交，防止超时

### 信息管理
- **公告系统**: 重要通知和政策发布
- **分类筛选**: 按类型查看不同信息
- **详情查看**: 支持富文本内容展示

## 🔐 权限控制体系

### 用户状态分级
1. **未提交** (`not_submitted`): 未完善个人资料
2. **审核中** (`pending`): 已提交，等待审核
3. **已认证** (`approved`): 审核通过，正式用户
4. **被拒绝** (`rejected`): 审核未通过

### 功能权限矩阵
| 功能模块 | 未提交 | 审核中 | 已认证 | 被拒绝 |
|---------|--------|--------|--------|--------|
| 信息浏览 | ✅ | ✅ | ✅ | ✅ |
| 题库练习 | ❌ | 限制 | ✅ | ❌ |
| 在线考试 | ❌ | ❌ | ✅ | ❌ |
| 证书查看 | ❌ | ❌ | ✅ | ❌ |

## 🎨 UI设计规范

### 色彩规范
- **主色调**: #2E8B57 (海绿色)
- **辅助色**: #3CB371, #228B22
- **功能色**:
  - 成功: #4CAF50
  - 警告: #FF9500
  - 错误: #f56c6c
  - 信息: #4A90E2

### 组件规范
- **卡片圆角**: 16rpx - 24rpx
- **按钮圆角**: 40rpx (圆形按钮)
- **间距规范**: 8rpx, 16rpx, 24rpx, 32rpx, 40rpx
- **字体大小**: 24rpx, 26rpx, 28rpx, 30rpx, 32rpx, 36rpx

## 📱 性能优化

### 包体积优化
- **分包加载**: 按需加载，减少主包体积
- **图片压缩**: 使用webp格式，减少资源大小
- **组件按需引入**: 避免全量引入UI组件库

### 运行性能
- **虚拟列表**: 长列表性能优化
- **图片懒加载**: 减少内存占用
- **缓存策略**: 合理使用本地缓存

## 🔧 开发工具配置

### VSCode推荐插件
- **Vetur**: Vue语法支持
- **TypeScript Importer**: 自动导入类型
- **SCSS IntelliSense**: SCSS语法支持
- **uni-app-schemas**: uni-app配置文件支持

### 代码格式化
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 📝 更新日志

### v1.0.0 (2024-12-XX)
**🎉 初始版本发布**

**✨ 新功能**
- 完整的用户认证系统（微信登录+资料审核）
- 信息中心模块（公告浏览+分类筛选）
- 学习中心模块（题库练习+学习统计）
- 考试中心模块（线上考试+人脸识别）
- 个人中心模块（信息管理+系统设置）

**🛠️ 技术特性**
- Vue3 + TypeScript + uview-plus技术栈
- 完整的权限控制和状态管理
- 组件化开发和模块化架构
- 响应式设计和现代化UI

**📱 支持平台**
- 微信小程序
- H5（移动端）
- App（Android/iOS）

---

*本文档最后更新时间: 2024年12月*
*项目版本: v1.0.0*
