{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": false, "declarationMap": false, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/utils/*": ["./src/utils/*"], "@/api/*": ["./src/api/*"], "@/types/*": ["./src/types/*"], "@/stores/*": ["./src/stores/*"]}, "types": ["@dcloudio/types", "@types/wechat-miniprogram"], "lib": ["ES2020", "DOM", "DOM.Iterable"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "pages/**/*.vue", "components/**/*.vue", "*.vue", "*.ts"], "exclude": ["node_modules", "dist", "unpackage"], "vueCompilerOptions": {"target": 3}}