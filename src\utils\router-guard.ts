import { permissionManager } from './permission'
import { PAGE_PATHS } from '../constants'

/**
 * 路由守卫配置
 */
interface RouteGuardConfig {
  // 需要登录的页面
  authRequired: string[]
  // 需要认证的页面
  authenticationRequired: string[]
  // 公开页面（无需任何权限）
  publicPages: string[]
  // 默认重定向页面
  defaultRedirect: string
}

const routeGuardConfig: RouteGuardConfig = {
  authRequired: [
    PAGE_PATHS.INFO,
    PAGE_PATHS.STUDY,
    PAGE_PATHS.EXAM,
    PAGE_PATHS.PERSONAL,
    // 分包页面
    PAGE_PATHS.EXAM_ONLINE_READING,
    PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
    PAGE_PATHS.EXAM_ONLINE_ANSWER,
    PAGE_PATHS.EXAM_OFFLINE_DETAIL,
    PAGE_PATHS.EXAM_HISTORY,
    PAGE_PATHS.PERSONAL_INFO,
    PAGE_PATHS.PERSONAL_CERTIFICATE,
    PAGE_PATHS.PERSONAL_FEEDBACK,
    PAGE_PATHS.PERSONAL_ABOUT,
  ],
  authenticationRequired: [
    PAGE_PATHS.EXAM,
    PAGE_PATHS.EXAM_ONLINE_READING,
    PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
    PAGE_PATHS.EXAM_ONLINE_ANSWER,
    PAGE_PATHS.EXAM_OFFLINE_DETAIL,
    PAGE_PATHS.EXAM_HISTORY,
    PAGE_PATHS.PERSONAL_CERTIFICATE,
  ],
  publicPages: [
    PAGE_PATHS.LOGIN,
    PAGE_PATHS.PROFILE,
  ],
  defaultRedirect: PAGE_PATHS.INFO,
}

/**
 * 路由守卫类
 */
export class RouterGuard {
  private config: RouteGuardConfig

  constructor(config: RouteGuardConfig) {
    this.config = config
  }

  /**
   * 检查页面访问权限
   */
  async checkAccess(url: string): Promise<{
    allowed: boolean
    redirectTo?: string
    message?: string
  }> {
    // 提取页面路径（去除参数）
    const pagePath = url.split('?')[0]

    // 公开页面直接允许访问
    if (this.config.publicPages.includes(pagePath)) {
      return { allowed: true }
    }

    // 检查是否需要登录
    if (this.config.authRequired.includes(pagePath)) {
      if (!permissionManager.isLoggedIn()) {
        return {
          allowed: false,
          redirectTo: PAGE_PATHS.LOGIN,
          message: '请先登录'
        }
      }
    }

    // 检查是否需要认证
    if (this.config.authenticationRequired.includes(pagePath)) {
      if (!permissionManager.isAuthenticated()) {
        const userStatus = permissionManager.getUserStatus()
        
        if (userStatus === 'not_submitted') {
          return {
            allowed: false,
            redirectTo: PAGE_PATHS.PROFILE,
            message: '请先完善个人资料'
          }
        } else if (userStatus === 'pending') {
          return {
            allowed: false,
            redirectTo: PAGE_PATHS.PERSONAL,
            message: '个人资料审核中，暂时无法使用此功能'
          }
        } else if (userStatus === 'rejected') {
          return {
            allowed: false,
            redirectTo: PAGE_PATHS.PROFILE,
            message: '资料审核未通过，请重新提交'
          }
        } else {
          return {
            allowed: false,
            redirectTo: PAGE_PATHS.PERSONAL,
            message: '请先完善个人资料并通过审核'
          }
        }
      }
    }

    return { allowed: true }
  }

  /**
   * 执行路由守卫
   */
  async guard(url: string): Promise<boolean> {
    const result = await this.checkAccess(url)

    if (!result.allowed) {
      if (result.message) {
        uni.showToast({
          title: result.message,
          icon: 'none',
          duration: 2000
        })
      }

      if (result.redirectTo) {
        // 延迟跳转，确保toast显示
        setTimeout(() => {
          if (result.redirectTo === PAGE_PATHS.LOGIN) {
            uni.reLaunch({
              url: result.redirectTo
            })
          } else if (this.config.authRequired.includes(result.redirectTo)) {
            uni.switchTab({
              url: result.redirectTo
            })
          } else {
            uni.redirectTo({
              url: result.redirectTo
            })
          }
        }, 1500)
      }

      return false
    }

    return true
  }

  /**
   * 获取默认首页
   */
  getDefaultPage(): string {
    if (!permissionManager.isLoggedIn()) {
      return PAGE_PATHS.LOGIN
    }

    const userStatus = permissionManager.getUserStatus()
    if (userStatus === 'not_submitted') {
      return PAGE_PATHS.PROFILE
    }

    return this.config.defaultRedirect
  }
}

// 创建全局路由守卫实例
export const routerGuard = new RouterGuard(routeGuardConfig)

/**
 * 页面跳转拦截器
 */
export function interceptNavigation() {
  // 拦截 uni.navigateTo
  const originalNavigateTo = uni.navigateTo
  uni.navigateTo = function(options: any) {
    routerGuard.guard(options.url).then(allowed => {
      if (allowed) {
        originalNavigateTo.call(uni, options)
      }
    })
  }

  // 拦截 uni.redirectTo
  const originalRedirectTo = uni.redirectTo
  uni.redirectTo = function(options: any) {
    routerGuard.guard(options.url).then(allowed => {
      if (allowed) {
        originalRedirectTo.call(uni, options)
      }
    })
  }

  // 拦截 uni.switchTab
  const originalSwitchTab = uni.switchTab
  uni.switchTab = function(options: any) {
    routerGuard.guard(options.url).then(allowed => {
      if (allowed) {
        originalSwitchTab.call(uni, options)
      }
    })
  }
}

/**
 * 页面权限检查 mixin
 */
export const pagePermissionMixin = {
  onLoad() {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const route = currentPage.route

    // 检查页面访问权限
    routerGuard.guard(`/${route}`).then(allowed => {
      if (!allowed) {
        // 权限检查失败，页面会被重定向
        console.log('Page access denied:', route)
      }
    })
  }
}

/**
 * 初始化路由守卫
 */
export function initRouterGuard() {
  // 启用导航拦截
  interceptNavigation()
  
  console.log('Router guard initialized')
}
