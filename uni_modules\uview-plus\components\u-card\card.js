/*
 * <AUTHOR> jry
 * @Description  :
 * @version      : 3.0
 * @Date         : 2025-04-26 16:37:21
 * @LastAuthor   : jry
 * @lastTime     : 2025-04-26 16:37:21
 * @FilePath     : /uview-plus/libs/config/props/card.js
 */
export default {
	// card组件的props
	card: {
		full: false,
		title: '',
		titleColor: '#303133',
		titleSize: '15px',
		subTitle: '',
		subTitleColor: '#909399',
		subTitleSize: '13px',
		border: true,
		index: '',
		margin: '15px',
		borderRadius: '8px',
		headStyle: {},
		bodyStyle: {},
		footStyle: {},
		headBorderBottom: true,
		footBorderTop: true,
		thumb: '',
		thumbWidth: '30px',
		thumbCircle: false,
		padding: '15px',
		paddingHead: '',
        paddingBody: '',
        paddingFoot: '',
        showHead: true,
        showFoot: true,
        boxShadow: 'none'
	}
}
