<template>
  <view class="profile-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <view class="header-bg"></view>
      <view class="user-info">
        <image 
          :src="userInfo.avatarUrl || defaultAvatar" 
          class="user-avatar" 
          mode="aspectFill"
        />
        <view class="user-details">
          <text class="user-name">{{ userInfo.nickName || '用户' }}</text>
          <view class="user-status" :class="userStore.authStatus">
            <u-icon :name="getStatusIcon()" :color="getStatusColor()" size="24" />
            <text>{{ getStatusText() }}</text>
          </view>
        </view>
        <view class="settings-btn" @click="goToSettings">
          <u-icon name="setting" color="#fff" size="44" />
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="main-content">
      <!-- 个人信息管理 -->
      <view class="menu-section">
        <view class="section-title">
          <text>个人信息</text>
        </view>
        
        <view class="menu-list">
          <view 
            class="menu-item" 
            @click="handlePersonalInfo"
          >
            <view class="menu-icon personal-info">
              <u-icon name="account" color="#4A90E2" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">{{ getPersonalInfoTitle() }}</text>
              <text class="menu-desc">{{ getPersonalInfoDesc() }}</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <!-- 正式用户才显示证书管理 -->
          <view 
            v-if="userStore.isVerified"
            class="menu-item" 
            @click="gotoCertificates"
          >
            <view class="menu-icon certificates">
              <u-icon name="medal" color="#FF9500" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">证书管理</text>
              <text class="menu-desc">查看和管理您的电子证书</text>
            </view>
            <view class="menu-badge" v-if="hasNewCertificate">
              <text>新</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
      
      <!-- 应用功能 -->
      <view class="menu-section">
        <view class="section-title">
          <text>应用功能</text>
        </view>
        
        <view class="menu-list">
          <view class="menu-item" @click="gotoFeedback">
            <view class="menu-icon feedback">
              <u-icon name="chat" color="#67C23A" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">投诉与建议</text>
              <text class="menu-desc">意见反馈，帮助我们改进</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <view class="menu-item" @click="gotoAbout">
            <view class="menu-icon about">
              <u-icon name="info-circle" color="#909399" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">关于我们</text>
              <text class="menu-desc">了解应用信息和版本</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <!-- VIP预留入口 -->
          <view class="menu-item vip-item" @click="handleVip">
            <view class="menu-icon vip">
              <u-icon name="diamond" color="#FFD700" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">VIP会员</text>
              <text class="menu-desc">无限练习，专享特权</text>
            </view>
            <view class="vip-tag">
              <text>即将上线</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
      
      <!-- 账户操作 -->
      <view class="menu-section">
        <view class="logout-btn" @click="handleLogout">
          <text>退出登录</text>
        </view>
      </view>
      
      <!-- 版本信息 -->
      <view class="version-info">
        <text class="version-text">版本号：v1.0.0</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

// Store
const userStore = useUserStore()

// 系统信息
const statusBarHeight = ref(0)

// 用户信息
const userInfo = ref({
  avatarUrl: '',
  nickName: ''
})

// 默认头像
const defaultAvatar = 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=200&h=200&fit=crop&crop=center'

// 是否有新证书
const hasNewCertificate = ref(false)

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 加载用户信息
  loadUserInfo()
})

// 获取状态图标
const getStatusIcon = () => {
  const iconMap = {
    'verified': 'checkmark-circle',
    'pending': 'clock',
    'rejected': 'close-circle',
    'not_submitted': 'account'
  }
  return iconMap[userStore.authStatus] || 'account'
}

// 获取状态颜色
const getStatusColor = () => {
  const colorMap = {
    'verified': '#4CAF50',
    'pending': '#FF9500',
    'rejected': '#f56c6c',
    'not_submitted': '#909399'
  }
  return colorMap[userStore.authStatus] || '#909399'
}

// 获取状态文本
const getStatusText = () => {
  const textMap = {
    'verified': '已认证',
    'pending': '审核中',
    'rejected': '审核未通过',
    'not_submitted': '未认证'
  }
  return textMap[userStore.authStatus] || '未认证'
}

// 获取个人信息标题
const getPersonalInfoTitle = () => {
  const titleMap = {
    'verified': '个人信息',
    'pending': '审核状态',
    'rejected': '重新提交资料',
    'not_submitted': '完善个人资料'
  }
  return titleMap[userStore.authStatus] || '个人信息'
}

// 获取个人信息描述
const getPersonalInfoDesc = () => {
  const descMap = {
    'verified': '查看个人资料和证书信息',
    'pending': '资料审核中，请耐心等待',
    'rejected': '资料审核未通过，请重新提交',
    'not_submitted': '完善资料以使用完整功能'
  }
  return descMap[userStore.authStatus] || '管理个人资料'
}

// 处理个人信息点击
const handlePersonalInfo = () => {
  if (userStore.authStatus === 'verified') {
    // 已认证用户，查看个人信息
    uni.navigateTo({
      url: '/pages/profile/detail'
    })
  } else {
    // 未认证用户，跳转到资料提交页
    uni.navigateTo({
      url: '/pages/profile/submit'
    })
  }
}

// 跳转到证书管理
const gotoCertificates = () => {
  uni.navigateTo({
    url: '/pages/profile/certificates'
  })
}

// 跳转到投诉建议
const gotoFeedback = () => {
  uni.navigateTo({
    url: '/pages/profile/feedback'
  })
}

// 跳转到关于我们
const gotoAbout = () => {
  uni.navigateTo({
    url: '/pages/profile/about'
  })
}

// 处理VIP点击
const handleVip = () => {
  uni.showModal({
    title: 'VIP功能即将上线',
    content: 'VIP会员功能正在开发中，上线后将为您提供无限练习次数和更多特权功能。',
    confirmText: '期待上线',
    showCancel: false
  })
}

// 跳转到设置
const goToSettings = () => {
  uni.showToast({
    title: '设置功能开发中',
    icon: 'none'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除用户信息
        userStore.logout()
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
      }
    }
  })
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    // 这里应该调用真实的API获取用户信息
    // const response = await getUserInfo()
    // userInfo.value = response.data
    
    // 模拟数据
    userInfo.value = {
      avatarUrl: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=200&h=200&fit=crop&crop=center',
      nickName: '张医生'
    }
    
    console.log('用户信息加载完成')
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.profile-center-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.profile-header {
  position: relative;
  height: 320rpx;
  overflow: hidden;
  
  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -50rpx;
      left: 0;
      right: 0;
      height: 100rpx;
      background: #f8f9fa;
      border-radius: 50rpx 50rpx 0 0;
    }
  }
  
  .user-info {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    padding: 40rpx 30rpx;
    
    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-right: 30rpx;
      border: 6rpx solid rgba(255, 255, 255, 0.3);
    }
    
    .user-details {
      flex: 1;
      
      .user-name {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #fff;
        margin-bottom: 16rpx;
      }
      
      .user-status {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 0.2);
        width: fit-content;
        
        text {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #fff;
        }
        
        &.verified {
          background: rgba(76, 175, 80, 0.3);
        }
        
        &.pending {
          background: rgba(255, 149, 0, 0.3);
        }
        
        &.rejected {
          background: rgba(245, 108, 108, 0.3);
        }
      }
    }
    
    .settings-btn {
      width: 80rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.main-content {
  padding: 0 30rpx 120rpx; // 为底部导航留空间
  margin-top: -50rpx;
  position: relative;
  z-index: 3;
}

.menu-section {
  margin-bottom: 40rpx;
  
  .section-title {
    margin-bottom: 20rpx;
    
    text {
      font-size: 28rpx;
      font-weight: bold;
      color: #666;
    }
  }
  
  .menu-list {
    background: #fff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    .menu-item {
      display: flex;
      align-items: center;
      padding: 40rpx;
      border-bottom: 2rpx solid #f8f9fa;
      position: relative;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.vip-item {
        background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
      }
      
      .menu-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 30rpx;
        
        &.personal-info {
          background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        
        &.certificates {
          background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
        }
        
        &.feedback {
          background: linear-gradient(135deg, #f1f8e9 0%, #c8e6c9 100%);
        }
        
        &.about {
          background: linear-gradient(135deg, #fafafa 0%, #e0e0e0 100%);
        }
        
        &.vip {
          background: linear-gradient(135deg, #fff9c4 0%, #ffeb3b 100%);
        }
      }
      
      .menu-content {
        flex: 1;
        
        .menu-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .menu-desc {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .menu-badge {
        padding: 4rpx 12rpx;
        background: #f56c6c;
        border-radius: 10rpx;
        margin-right: 20rpx;
        
        text {
          font-size: 20rpx;
          color: #fff;
        }
      }
      
      .vip-tag {
        padding: 6rpx 16rpx;
        background: #FF9500;
        border-radius: 12rpx;
        margin-right: 20rpx;
        
        text {
          font-size: 20rpx;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
  
  .logout-btn {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    text-align: center;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    text {
      font-size: 30rpx;
      color: #f56c6c;
      font-weight: bold;
    }
  }
}

.version-info {
  text-align: center;
  margin-top: 40rpx;
  
  .version-text {
    font-size: 24rpx;
    color: #999;
  }
}
</style> 