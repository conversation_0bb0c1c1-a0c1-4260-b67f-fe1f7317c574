<template>
  <view class="study-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">学习中心</text>
        <view class="header-actions">
          <view class="study-stats">
            <text class="stats-text">今日已练习 {{ studyStore.dailyPracticeCount }}/{{ maxDailyPractice }} 组</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用户状态提示 -->
    <UserStatusBanner :showAction="true" />
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 学习统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.totalQuestions }}</text>
          <text class="stats-label">累计练习</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.accuracy }}%</text>
          <text class="stats-label">正确率</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.totalSessions }}</text>
          <text class="stats-label">练习次数</text>
        </view>
      </view>
      
      <!-- 学习模块 -->
      <view class="study-modules">
        <!-- 教材学习模块（预留） -->
        <view class="module-card disabled" @click="handleTextbookClick">
          <view class="module-icon textbook">
            <u-icon name="book" color="#4A90E2" size="80" />
            <view class="coming-soon-badge">
              <text>即将上线</text>
            </view>
          </view>
          <view class="module-content">
            <text class="module-title">教材学习</text>
            <text class="module-desc">系统性学习专业教材内容</text>
            <text class="module-status">功能建设中，敬请期待</text>
          </view>
          <u-icon name="arrow-right" color="#c0c4cc" size="32" />
        </view>
        
        <!-- 题库练习模块 -->
        <PermissionWrapper permission="auth" :showFallback="false">
          <view class="module-card" @click="handleQuestionBankClick">
            <view class="module-icon question-bank">
              <u-icon name="edit-pen" color="#FF9500" size="80" />
              <view v-if="!studyStore.canPracticeToday" class="limit-badge">
                <text>今日已达上限</text>
              </view>
            </view>
            <view class="module-content">
              <text class="module-title">题库练习</text>
              <text class="module-desc">分类题库专项练习，即时反馈</text>
              <text class="module-info">{{ getQuestionBankInfo() }}</text>
            </view>
            <u-icon name="arrow-right" color="#4A90E2" size="32" />
          </view>
        </PermissionWrapper>
        
        <!-- 未登录用户提示 -->
        <PermissionWrapper permission="auth" :showFallback="true">
          <template #fallback>
            <view class="module-card login-prompt" @click="goToLogin">
              <view class="module-icon login">
                <u-icon name="account" color="#2E8B57" size="80" />
              </view>
              <view class="module-content">
                <text class="module-title">登录后开始学习</text>
                <text class="module-desc">登录后可使用题库练习功能</text>
                <text class="module-info">每日免费练习3组题目</text>
              </view>
              <u-icon name="arrow-right" color="#2E8B57" size="32" />
            </view>
          </template>
        </PermissionWrapper>
      </view>
      
      <!-- VIP特权预告（预留） -->
      <view v-if="userStore.isLoggedIn" class="vip-preview">
        <view class="vip-card">
          <view class="vip-header">
            <view class="vip-icon">
              <u-icon name="diamond" color="#FFD700" size="60" />
            </view>
            <view class="vip-content">
              <text class="vip-title">VIP会员特权</text>
              <text class="vip-desc">无限练习次数 · 更多题库内容 · 专属学习报告</text>
            </view>
          </view>
          <view class="vip-features">
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>无限制刷题练习</text>
            </view>
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>专享VIP题库</text>
            </view>
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>详细学习报告</text>
            </view>
          </view>
          <u-button 
            class="vip-btn" 
            type="warning"
            @click="handleVipClick"
          >
            即将开放，敬请期待
          </u-button>
        </view>
      </view>
      
      <!-- 最近练习记录 -->
      <view v-if="userStore.isLoggedIn && studyStore.practiceHistory.length > 0" class="recent-practices">
        <view class="section-header">
          <text class="section-title">最近练习</text>
          <text class="section-more" @click="viewAllPractices">查看全部</text>
        </view>
        
        <view class="practice-list">
          <view 
            v-for="practice in recentPractices" 
            :key="practice.id"
            class="practice-item"
            @click="viewPracticeDetail(practice)"
          >
            <view class="practice-info">
              <text class="practice-category">{{ getCategoryName(practice.categoryId) }}</text>
              <text class="practice-time">{{ formatRelativeTime(practice.endTime || practice.startTime) }}</text>
            </view>
            <view class="practice-result">
              <text class="score" :class="getScoreClass(practice.score || 0)">
                {{ practice.score || 0 }}分
              </text>
              <text class="accuracy">{{ practice.correctCount || 0 }}/{{ practice.totalCount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '../../src/stores/user'
import { useAppStore } from '../../src/stores/app'
import { useStudyStore } from '../../src/stores/study'
import { permissionManager } from '../../src/utils/permission'
import { formatRelativeTime } from '../../src/utils'
import { PAGE_PATHS, PRACTICE_CONFIG } from '../../src/constants'
import api from '../../src/api'

// 导入组件
import UserStatusBanner from '../../src/components/common/UserStatusBanner.vue'
import PermissionWrapper from '../../src/components/common/PermissionWrapper.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()
const studyStore = useStudyStore()

// 系统信息
const statusBarHeight = ref(0)

// 配置
const maxDailyPractice = PRACTICE_CONFIG.QUESTIONS_PER_SESSION

// 计算属性
const studyStats = computed(() => studyStore.getSessionStats())

const recentPractices = computed(() => {
  return studyStore.practiceHistory.slice(0, 3)
})

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 初始化数据
  initData()
})

onShow(() => {
  // 页面显示时刷新数据
  if (userStore.isLoggedIn) {
    loadStudyData()
  }
})

// 初始化数据
const initData = async () => {
  if (userStore.isLoggedIn) {
    await loadStudyData()
    await loadCategories()
  }
}

// 加载学习数据
const loadStudyData = async () => {
  try {
    // 这里可以调用API获取最新的学习统计
    // const response = await api.study.getStudyStats()
    // 更新本地统计数据
  } catch (error) {
    console.error('加载学习数据失败:', error)
  }
}

// 加载题库分类
const loadCategories = async () => {
  try {
    const response = await api.study.getCategories()
    studyStore.setCategories(response.data)
  } catch (error) {
    console.error('加载题库分类失败:', error)
  }
}

// 获取题库信息
const getQuestionBankInfo = () => {
  if (!userStore.isLoggedIn) {
    return '登录后可免费练习'
  }
  
  if (userStore.isAuthenticated) {
    return '认证用户可无限练习'
  }
  
  if (!studyStore.canPracticeToday) {
    return '免费练习已用完，明日可继续'
  }
  
  return `免费练习 ${studyStore.remainingPracticeCount} 组剩余`
}

// 获取分类名称
const getCategoryName = (categoryId: string) => {
  const category = studyStore.categories.find(c => c.id === categoryId)
  return category?.name || '未知分类'
}

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 80) return 'good'
  if (score >= 60) return 'normal'
  return 'poor'
}

// 教材点击事件
const handleTextbookClick = () => {
  appStore.showToast('教材功能正在建设中，敬请期待')
}

// 题库练习点击事件
const handleQuestionBankClick = () => {
  // 检查权限
  if (!permissionManager.enforceFeatureAccess('practice')) {
    return
  }
  
  if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {
    appStore.showModal({
      title: '今日练习已达上限',
      content: '免费用户每天可练习3组题目，明天可继续练习。完善个人资料并通过审核后可享受无限练习特权。',
      confirmText: '完善资料',
      cancelText: '我知道了'
    }).then((confirmed) => {
      if (confirmed) {
        appStore.switchTab(PAGE_PATHS.PERSONAL)
      }
    })
    return
  }
  
  // 跳转到题库分类页面
  appStore.navigateTo(PAGE_PATHS.STUDY_CATEGORY)
}

// 登录
const goToLogin = () => {
  appStore.redirectTo(PAGE_PATHS.LOGIN)
}

// VIP点击事件
const handleVipClick = () => {
  appStore.showModal({
    title: 'VIP功能即将上线',
    content: 'VIP会员功能正在开发中，上线后将为您提供更丰富的学习功能和特权。',
    confirmText: '期待上线',
    showCancel: false
  })
}

// 查看所有练习记录
const viewAllPractices = () => {
  appStore.showToast('练习历史功能开发中')
}

// 查看练习详情
const viewPracticeDetail = (practice: any) => {
  appStore.navigateTo(PAGE_PATHS.STUDY_SUMMARY, { sessionId: practice.id })
}
</script>

<style lang="scss" scoped>
@import '../../src/styles/global.scss';

.study-center-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      .study-stats {
        .stats-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx; // 为底部导航留空间
}

.stats-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  
  .stats-item {
    flex: 1;
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #4A90E2;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .stats-divider {
    width: 2rpx;
    height: 60rpx;
    background: #f0f0f0;
  }
}

.study-modules {
  margin-bottom: 40rpx;
  
  .module-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    
    &.disabled {
      opacity: 0.6;
      
      .module-content {
        .module-status {
          color: #999;
          font-size: 24rpx;
        }
      }
    }
    
    &.login-prompt {
      border: 2rpx dashed $acdc-primary;
      background: rgba(46, 139, 87, 0.05);
    }
    
    .module-icon {
      position: relative;
      margin-right: 40rpx;
      
      &.textbook {
        padding: 24rpx;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 20rpx;
      }
      
      &.question-bank {
        padding: 24rpx;
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
        border-radius: 20rpx;
      }
      
      &.login {
        padding: 24rpx;
        background: linear-gradient(135deg, rgba(46, 139, 87, 0.1) 0%, rgba(46, 139, 87, 0.2) 100%);
        border-radius: 20rpx;
      }
      
      .coming-soon-badge,
      .limit-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        border-radius: 12rpx;
        padding: 4rpx 12rpx;
        
        text {
          font-size: 18rpx;
          color: #fff;
          font-weight: bold;
        }
      }
      
      .coming-soon-badge {
        background: #FF9500;
      }
      
      .limit-badge {
        background: #f56c6c;
      }
    }
    
    .module-content {
      flex: 1;
      
      .module-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .module-desc {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .module-info {
        display: block;
        font-size: 24rpx;
        color: #4A90E2;
      }
      
      .module-status {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.vip-preview {
  margin-bottom: 40rpx;
  
  .vip-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 40rpx;
    color: #fff;
    
    .vip-header {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;
      
      .vip-icon {
        margin-right: 24rpx;
      }
      
      .vip-content {
        flex: 1;
        
        .vip-title {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 12rpx;
        }
        
        .vip-desc {
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }
    
    .vip-features {
      margin-bottom: 40rpx;
      
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        text {
          margin-left: 16rpx;
          font-size: 26rpx;
        }
      }
    }
    
    .vip-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      color: #fff;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }
  }
}

.recent-practices {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .section-more {
      font-size: 26rpx;
      color: #4A90E2;
    }
  }
  
  .practice-list {
    .practice-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .practice-info {
        flex: 1;
        
        .practice-category {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .practice-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .practice-result {
        text-align: right;
        
        .score {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
          
          &.good {
            color: #4CAF50;
          }
          
          &.normal {
            color: #FF9500;
          }
          
          &.poor {
            color: #f56c6c;
          }
        }
        
        .accuracy {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
