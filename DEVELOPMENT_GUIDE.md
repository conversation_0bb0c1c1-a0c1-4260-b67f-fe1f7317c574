# 疾控医护考试系统 - 开发指南

## 🚀 快速开始

### 环境要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **HBuilderX**: 最新版本
- **微信开发者工具**: 最新版本

### 安装依赖
```bash
# 克隆项目
git clone [项目地址]
cd 疾控医护考试系统

# 安装依赖
npm install

# 启动开发服务器
npm run dev:mp-weixin
```

### 开发环境配置
1. 使用HBuilderX打开项目
2. 配置微信小程序AppID
3. 启动微信开发者工具
4. 导入项目目录 `dist/dev/mp-weixin`

## 📋 开发规范

### 代码规范

#### 文件命名
- **页面文件**: kebab-case (如: `user-profile.vue`)
- **组件文件**: PascalCase (如: `UserCard.vue`)
- **工具文件**: camelCase (如: `formatDate.ts`)

#### 组件开发规范
```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props定义
interface Props {
  title: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// Emits定义
interface Emits {
  (e: 'click', value: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)

// 计算属性
const displayText = computed(() => {
  return `${props.title} (${props.count})`
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法
const handleClick = () => {
  emit('click', 'value')
}
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

#### TypeScript规范
```typescript
// 类型定义
interface User {
  id: string
  name: string
  email?: string
}

// API响应类型
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 枚举定义
enum UserStatus {
  NOT_SUBMITTED = 'not_submitted',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}
```

### 样式规范

#### SCSS变量使用
```scss
// 使用全局变量
.component {
  background: $acdc-primary;
  color: $acdc-text-primary;
  border-radius: $acdc-border-radius;
}
```

#### BEM命名规范
```scss
.user-card {
  &__header {
    // 元素样式
  }
  
  &__title {
    // 元素样式
    
    &--large {
      // 修饰符样式
    }
  }
  
  &--featured {
    // 修饰符样式
  }
}
```

## 🔧 核心功能开发

### 状态管理 (Pinia)

#### Store定义
```typescript
// stores/user.ts
import { defineStore } from 'pinia'

interface UserState {
  userInfo: UserInfo | null
  isLoggedIn: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    isLoggedIn: false
  }),
  
  getters: {
    isAuthenticated: (state) => {
      return state.userInfo?.status === 'approved'
    }
  },
  
  actions: {
    async login(code: string) {
      // 登录逻辑
    },
    
    logout() {
      this.userInfo = null
      this.isLoggedIn = false
    }
  }
})
```

#### Store使用
```vue
<script setup lang="ts">
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 访问状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 调用方法
const handleLogin = () => {
  userStore.login(code)
}
</script>
```

### API接口开发

#### 接口定义
```typescript
// api/user.ts
import { request } from '@/utils/request'

export const userApi = {
  // 登录
  login: (data: LoginParams) => 
    request<LoginResponse>('/auth/login', 'POST', data),
  
  // 获取用户信息
  getUserInfo: () => 
    request<UserInfo>('/user/info', 'GET'),
  
  // 更新用户信息
  updateUserInfo: (data: UpdateUserParams) => 
    request<void>('/user/update', 'PUT', data)
}
```

#### 请求封装
```typescript
// utils/request.ts
interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
}

export const request = <T>(
  url: string, 
  method: RequestConfig['method'], 
  data?: any
): Promise<ApiResponse<T>> => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: `${BASE_URL}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': getToken(),
        ...header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data as ApiResponse<T>)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
```

### 权限控制开发

#### 权限组件
```vue
<!-- PermissionWrapper.vue -->
<template>
  <view v-if="hasPermission">
    <slot />
  </view>
  <view v-else-if="showFallback">
    <slot name="fallback">
      <EmptyState 
        type="no-permission"
        title="权限不足"
        description="您暂无访问权限"
      />
    </slot>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { permissionManager } from '@/utils/permission'

interface Props {
  permission: string
  showFallback?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFallback: true
})

const userStore = useUserStore()

const hasPermission = computed(() => {
  return permissionManager.hasPermission(
    props.permission, 
    userStore.userInfo
  )
})
</script>
```

#### 权限使用
```vue
<template>
  <PermissionWrapper permission="authenticated">
    <view>需要认证的内容</view>
    
    <template #fallback>
      <view>未认证用户看到的内容</view>
    </template>
  </PermissionWrapper>
</template>
```

## 🧪 测试开发

### 单元测试
```typescript
// tests/utils/formatDate.test.ts
import { formatDate } from '@/utils'

describe('formatDate', () => {
  test('should format date correctly', () => {
    const date = new Date('2024-12-01')
    expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-12-01')
  })
})
```

### 组件测试
```typescript
// tests/components/UserCard.test.ts
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  test('should render user name', () => {
    const wrapper = mount(UserCard, {
      props: {
        user: { name: 'Test User' }
      }
    })
    
    expect(wrapper.text()).toContain('Test User')
  })
})
```

## 📱 调试技巧

### 微信小程序调试
1. **控制台调试**: 使用 `console.log` 输出调试信息
2. **网络调试**: 在开发者工具中查看网络请求
3. **存储调试**: 查看本地存储和缓存数据
4. **性能调试**: 使用性能面板分析性能问题

### 真机调试
1. **预览调试**: 生成二维码真机预览
2. **远程调试**: 使用真机调试功能
3. **日志收集**: 集成日志收集工具

## 🚀 部署流程

### 开发环境部署
```bash
# 构建开发版本
npm run build:mp-weixin

# 上传到微信小程序后台
# 使用微信开发者工具上传代码
```

### 生产环境部署
```bash
# 构建生产版本
npm run build:mp-weixin --mode production

# 代码审核
# 在微信小程序后台提交审核

# 发布上线
# 审核通过后发布上线
```

## 🔍 常见问题

### 开发问题
1. **组件不显示**: 检查组件导入路径和注册
2. **样式不生效**: 检查样式作用域和选择器优先级
3. **接口请求失败**: 检查网络配置和请求参数

### 性能问题
1. **页面加载慢**: 优化图片大小和网络请求
2. **内存占用高**: 检查内存泄漏和组件销毁
3. **包体积过大**: 使用分包和按需加载

### 兼容性问题
1. **iOS兼容性**: 测试iOS设备的特殊情况
2. **Android兼容性**: 处理不同Android版本差异
3. **小程序限制**: 遵循小程序开发规范

## 📚 学习资源

### 官方文档
- [uni-app官方文档](https://uniapp.dcloud.net.cn/)
- [Vue3官方文档](https://cn.vuejs.org/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [uview-plus文档](https://uviewui.com/)

### 推荐工具
- **代码编辑器**: VSCode + Vetur插件
- **版本控制**: Git + GitHub/GitLab
- **接口测试**: Postman/Apifox
- **设计工具**: Figma/Sketch

---

*本指南持续更新中，如有问题请提交Issue*
