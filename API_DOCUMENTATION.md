# 疾控医护考试系统 - API接口文档

## 📋 接口概述

本文档描述了疾控医护考试系统的后端API接口规范，包括用户认证、学习管理、考试管理等核心功能模块。

### 基础信息
- **Base URL**: `https://api.example.com/v1`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

### 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔐 用户认证模块

### 1. 微信登录
**接口地址**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "string",           // 微信授权码
  "encryptedData": "string",  // 加密数据
  "iv": "string"              // 初始向量
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "user_123",
      "openid": "wx_openid_123",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "status": "not_submitted"
    }
  }
}
```

### 2. 获取用户信息
**接口地址**: `GET /user/info`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "user_123",
    "realName": "张三",
    "idCard": "110101199001011234",
    "phone": "13800138000",
    "organization": "北京市疾控中心",
    "department": "免疫规划科",
    "position": "主治医师",
    "photo": "照片URL",
    "status": "approved",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 提交个人资料
**接口地址**: `POST /user/profile`

**请求参数**:
```json
{
  "realName": "张三",
  "idCard": "110101199001011234",
  "phone": "13800138000",
  "organization": "北京市疾控中心",
  "department": "免疫规划科",
  "position": "主治医师",
  "photo": "照片URL"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "status": "pending"
  }
}
```

## 📢 信息中心模块

### 1. 获取公告列表
**接口地址**: `GET /announcements`

**请求参数**:
```
category: string (可选) - 公告分类
page: number (可选) - 页码，默认1
limit: number (可选) - 每页数量，默认10
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "ann_123",
        "title": "关于开展2024年疫苗接种培训的通知",
        "summary": "为提高疫苗接种技能...",
        "category": "training",
        "isImportant": true,
        "publishTime": "2024-01-01T00:00:00Z",
        "readCount": 1250
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

### 2. 获取公告详情
**接口地址**: `GET /announcements/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "ann_123",
    "title": "关于开展2024年疫苗接种培训的通知",
    "content": "详细内容HTML",
    "category": "training",
    "isImportant": true,
    "publishTime": "2024-01-01T00:00:00Z",
    "readCount": 1251,
    "attachments": [
      {
        "name": "培训大纲.pdf",
        "url": "文件URL",
        "size": 1024000
      }
    ]
  }
}
```

## 📚 学习中心模块

### 1. 获取题库分类
**接口地址**: `GET /study/categories`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "cat_123",
      "name": "疾病预防",
      "description": "疾病预防相关知识",
      "questionCount": 500,
      "icon": "图标URL"
    }
  ]
}
```

### 2. 获取练习题目
**接口地址**: `GET /study/questions`

**请求参数**:
```
categoryId: string - 分类ID
count: number - 题目数量，默认10
difficulty: string (可选) - 难度等级
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "q_123",
      "title": "以下哪种疫苗属于减毒活疫苗？",
      "type": "single",
      "options": [
        "乙肝疫苗",
        "麻疹疫苗",
        "百白破疫苗",
        "流感疫苗"
      ],
      "answer": 1,
      "explanation": "麻疹疫苗是减毒活疫苗...",
      "difficulty": "medium",
      "score": 5
    }
  ]
}
```

### 3. 提交练习答案
**接口地址**: `POST /study/submit`

**请求参数**:
```json
{
  "categoryId": "cat_123",
  "answers": {
    "q_123": 1,
    "q_124": [0, 2],
    "q_125": true
  },
  "duration": 300
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "sessionId": "session_123",
    "score": 85,
    "correctCount": 8,
    "totalCount": 10,
    "accuracy": 80,
    "results": [
      {
        "questionId": "q_123",
        "isCorrect": true,
        "userAnswer": 1,
        "correctAnswer": 1
      }
    ]
  }
}
```

## 🎯 考试中心模块

### 1. 获取考试列表
**接口地址**: `GET /exams`

**请求参数**:
```
status: string (可选) - 考试状态
type: string (可选) - 考试类型 (online/offline)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "exam_123",
      "name": "2024年第二季度疾控医护任职资格考试",
      "description": "包含疫苗接种、预防保健等内容",
      "type": "online",
      "status": "not_started",
      "startTime": "2024-04-15T09:00:00Z",
      "endTime": "2024-04-15T11:00:00Z",
      "duration": 120,
      "totalScore": 100,
      "passScore": 60
    }
  ]
}
```

### 2. 获取考试详情
**接口地址**: `GET /exams/{id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "exam_123",
    "name": "考试名称",
    "description": "考试描述",
    "type": "online",
    "status": "not_started",
    "startTime": "2024-04-15T09:00:00Z",
    "endTime": "2024-04-15T11:00:00Z",
    "duration": 120,
    "totalScore": 100,
    "passScore": 60,
    "rules": [
      "考试时间120分钟",
      "总分100分，60分及格"
    ]
  }
}
```

### 3. 人脸识别验证
**接口地址**: `POST /exams/{id}/face-verify`

**请求参数**:
```json
{
  "photo": "base64图片数据"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "success": true,
    "similarity": 0.95,
    "verifyId": "verify_123"
  }
}
```

### 4. 开始考试
**接口地址**: `POST /exams/{id}/start`

**响应数据**:
```json
{
  "code": 200,
  "message": "考试开始",
  "data": {
    "recordId": "record_123",
    "questions": [
      {
        "id": "q_123",
        "title": "题目内容",
        "type": "single",
        "options": ["选项A", "选项B"],
        "score": 5
      }
    ]
  }
}
```

### 5. 提交考试答案
**接口地址**: `POST /exams/submit`

**请求参数**:
```json
{
  "recordId": "record_123",
  "answers": {
    "q_123": 1,
    "q_124": [0, 2]
  },
  "duration": 7200,
  "antiCheatLogs": [
    {
      "type": "app_hide",
      "timestamp": 1640995200000
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "score": 85,
    "passed": true,
    "correctCount": 17,
    "totalCount": 20
  }
}
```

## 📊 统计数据模块

### 1. 获取学习统计
**接口地址**: `GET /stats/study`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalSessions": 25,
    "totalQuestions": 250,
    "accuracy": 85,
    "totalDuration": 3600,
    "categoryStats": [
      {
        "categoryId": "cat_123",
        "categoryName": "疾病预防",
        "sessionCount": 10,
        "accuracy": 90
      }
    ]
  }
}
```

### 2. 获取考试统计
**接口地址**: `GET /stats/exam`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 12,
    "passed": 10,
    "pending": 2,
    "averageScore": 82,
    "bestScore": 95,
    "recentExams": [
      {
        "examId": "exam_123",
        "examName": "考试名称",
        "score": 85,
        "passed": true,
        "completedAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## 🔧 错误码说明

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 10001 | 用户不存在 |
| 10002 | 用户未认证 |
| 10003 | 权限不足 |
| 20001 | 题目不存在 |
| 20002 | 练习次数已达上限 |
| 30001 | 考试不存在 |
| 30002 | 考试未开始 |
| 30003 | 考试已结束 |
| 30004 | 人脸识别失败 |

### 错误响应示例
```json
{
  "code": 10002,
  "message": "用户未认证，请先完善个人资料",
  "data": null,
  "timestamp": 1640995200000
}
```

---

*本文档版本: v1.0.0*
*最后更新: 2024年12月*
