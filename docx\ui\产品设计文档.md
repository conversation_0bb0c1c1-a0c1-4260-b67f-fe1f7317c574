# 产品设计文档 - 疾控医护任职资格考试系统（小程序端）

**版本**：v1.0  
**日期**：2024年12月  
**技术栈**：uni-app + Vue3 + uview-plus

## 1. 产品概述

### 1.1 产品定位
一款专为疾控机构医护人员设计的任职资格考试及学习辅助平台。通过微信小程序提供从学习备考、模拟练习、考试报名、在线考试到证书管理的全流程服务。

### 1.2 目标用户
- **核心用户**：各级疾控机构在职医护人员
- **相关用户**：机构管理员（通过后台管理系统操作）

### 1.3 核心价值
- **便捷高效**：随时随地通过微信小程序进行学习、刷题、参与线上考试及报名线下考试
- **资源集中**：一站式获取考试公告、政策法规、学习资料
- **智能辅助**：个性化的考试提醒、清晰的考试状态追踪、便捷的证书查询与管理

## 2. 功能架构

### 2.1 核心功能模块

#### 模块1：登录/注册系统
- **微信授权登录**：一键微信授权登录
- **用户协议确认**：隐私政策和服务协议
- **个人资料提交**：包含本人照片上传（用于人脸识别）
- **审核状态管理**：支持跳过资料提交，先体验部分功能

#### 模块2：信息中心
- **公告管理**：考试公告、重要通知
- **政策法规**：相关政策文件
- **内容详情**：支持富文本展示
- **访问权限控制**：非正式用户显示认证提示

#### 模块3：学习中心
- **教材学习**：预留功能模块
- **题库练习**：
  - 分类题库（接种门诊/产科/犬伤门诊）
  - 多题型支持（单选/多选/判断/问答）
  - 即时反馈与答案解析
  - 免费限制（每天3组×10题）
  - VIP特权（预留）

#### 模块4：考试中心
- **线上考试**：
  - 考前阅读须知
  - 人脸识别身份验证
  - 在线答题（含防作弊机制）
  - 答卷提交（不即时出分）
- **线下考试**：
  - 考场时间查看
  - 场次预约报名
  - 取消预约功能
- **历史记录**：考试记录查询

#### 模块5：个人中心
- **个人信息**：用户资料展示（敏感信息脱敏）
- **证书管理**：
  - 当前证书状态
  - 历史证书查看
  - 电子证书下载
- **反馈系统**：投诉与建议
- **关于我们**：应用信息

### 2.2 用户权限体系
- **未登录用户**：仅可访问登录页
- **非正式用户**：
  - 未提交资料
  - 待审核状态
  - 审核未通过
- **正式用户**：完整功能访问权限

## 3. 核心业务流程

### 3.1 用户注册认证流程
```
微信授权登录 → 协议确认 → 个人资料提交（含照片上传）→ 机构审核 → 正式用户
                    ↓
              可选择跳过 → 体验部分功能
```

### 3.2 学习备考流程
```
进入学习中心 → 选择题库分类 → 刷题练习（10题/组）→ 即时反馈 → 继续练习或返回
```

### 3.3 线上考试流程
```
选择考试 → 考前阅读 → 人脸识别验证 → 在线答题 → 提交答卷 → 等待成绩公布
```

### 3.4 线下考试流程
```
查看考试详情 → 选择考场和时间 → 报名预约 → 参加考试 → 查看成绩
```

## 4. 页面架构与交互设计

### 4.1 页面结构总览

#### `登录页面`
**用途**：用户首次进入或未登录状态的授权登录入口
**核心功能**：
- 微信一键授权登录
- 用户协议勾选确认
- 协议文本查看
**交互说明**：
- 用户必须勾选协议才能点击登录按钮
- 点击登录后拉起微信授权弹窗
- 授权成功后根据用户状态跳转到相应页面（信息中心或个人资料提交页）
- 拒绝授权给出友好提示

#### `个人资料提交页面`
**用途**：新用户或未提交资料用户的信息收集页面
**核心功能**：
- 个人基本信息填写（姓名、手机、身份证）
- 本人照片上传（用于人脸识别）
- 隶属机构和职位选择（支持搜索）
- 提交审核或跳过功能
**交互说明**：
- 表单项实时校验，照片上传有大小和格式限制
- 机构和职位下拉支持搜索快速定位
- 提供"跳过，先去学习"选项，用户可暂时体验部分功能
- 提交成功后跳转到个人中心或信息中心

#### `信息中心页面`（底部Tab 1）
**用途**：展示考试公告、政策法规、重要通知的信息展示中心
**核心功能**：
- 公告列表展示（支持置顶和优先级）
- 政策法规列表
- 重要通知列表
- 内容详情查看
**交互说明**：
- 非正式用户显示身份状态提示和认证引导，不展示信息列表
- 正式用户可查看完整信息列表，支持下拉刷新、上拉加载更多
- 点击列表项进入详情页，支持富文本内容展示
- 重要信息通过标签或视觉强调突出显示

#### `信息详情页面`
**用途**：展示公告、政策、通知的详细内容
**核心功能**：
- 完整标题、日期、来源展示
- 富文本内容渲染
- 返回上级页面
**交互说明**：
- 支持富文本格式的内容展示
- 导航栏显示返回按钮
- 内容过长时支持滚动查看

#### `学习中心页面`（底部Tab 2）
**用途**：学习功能的主入口，提供教材和题库练习
**核心功能**：
- 教材学习模块入口（预留）
- 题库练习模块入口
- 学习进度展示（可选）
**交互说明**：
- 教材模块点击提示"功能建设中"
- 题库模块点击进入分类选择页面
- 模块展示采用卡片式布局，清晰区分功能区域

#### `题库分类选择页面`
**用途**：选择具体的题库分类进行练习
**核心功能**：
- 题库分类列表展示（接种门诊/产科/犬伤门诊）
- 各分类题目数量显示
- 进入对应分类练习
**交互说明**：
- 分类以列表或卡片形式展示，显示名称和题目总数
- 点击分类卡片进入对应的刷题练习页面
- 导航栏提供返回学习中心功能

#### `题库练习页面`
**用途**：进行题目练习和答题的核心学习页面
**核心功能**：
- 题目展示（单选/多选/判断/问答）
- 即时答案反馈和解析
- 练习进度跟踪（X/10）
- 上一题/下一题导航
**交互说明**：
- 顶部显示分类名称和进度条
- 题目区域根据题型展示不同的交互组件
- 用户作答后立即显示正确答案和解析（问答题除外）
- 底部提供上一题/下一题按钮，最后一题显示"提交本组练习"
- 完成一组练习后显示总结页面，可选择再练一组或返回

#### `练习总结页面`
**用途**：显示单组练习的结果统计
**核心功能**：
- 答题正确率统计
- 答对题数/总题数展示
- 继续练习或返回选择
**交互说明**：
- 清晰展示练习结果
- 提供"再练一组"和"返回题库分类"操作按钮
- 如达到每日免费限制，显示相应提示信息

#### `考试中心页面`（底部Tab 3）
**用途**：考试相关功能的主入口
**核心功能**：
- 本期考试（待考列表）展示
- 历史考试记录入口
- 考试状态跟踪
**交互说明**：
- 非正式用户显示"未认证，无法考试"提示和认证引导
- 正式用户显示考试卡片列表，区分线上/线下考试类型
- 考试卡片显示考试名称、类型、状态、有效期和操作按钮
- 操作按钮根据考试状态动态显示（开始考试/立即报名等）

#### `线上考试-考前阅读页面`
**用途**：线上考试前的须知阅读和确认
**核心功能**：
- 考前须知内容展示
- 阅读确认和进入下一步
**交互说明**：
- 展示后台配置的考前须知内容
- 用户阅读完毕确认后进入人脸识别环节
- 可设置倒计时或要求滚动到底才能确认

#### `线上考试-人脸识别页面`
**用途**：考试前的身份验证环节
**核心功能**：
- 调用前置摄像头拍照
- 与注册照片进行比对验证
- 验证结果处理
**交互说明**：
- 页面引导用户正确拍照（正脸、光线充足等）
- 同步等待后端比对结果
- 验证通过进入答题页面，失败可重试（最多3次）
- 多次失败中止考试并给出提示

#### `线上考试-答题页面`
**用途**：线上考试的核心答题界面
**核心功能**：
- 考试题目展示和作答
- 答题进度跟踪
- 倒计时显示
- 答题卡功能
- 防作弊监控
**交互说明**：
- 顶部显示考试名称、剩余时间倒计时、答题进度
- 题目区域支持各种题型，叠加考生信息水印
- 底部提供上一题/下一题导航和答题卡入口
- 答案实时本地保存，防止数据丢失
- 监控切屏行为，记录防作弊日志
- 时间到自动提交或用户主动交卷

#### `线下考试详情页面`
**用途**：查看线下考试的详细信息和进行报名
**核心功能**：
- 考试基本信息展示
- 考场和时间列表
- 名额查看和报名功能
- 取消报名功能
**交互说明**：
- 展示考试名称、说明等基本信息
- 考场列表支持多考场切换（标签页或卡片展开）
- 时间列表显示日期、时间段、剩余名额
- 报名按钮根据名额和用户状态动态显示
- 已报名用户显示报名信息和取消报名选项

#### `历史考试记录页面`
**用途**：查看用户过往的所有考试记录
**核心功能**：
- 历史考试列表展示
- 考试成绩和状态查看
- 考试详情查看
**交互说明**：
- 列表显示考试名称、类型、完成日期、成绩、状态
- 支持时间倒序排列和分页加载
- 点击记录可查看考试详情
- 无记录时显示友好的空状态提示

#### `个人中心页面`（底部Tab 4）
**用途**：个人信息管理和系统功能入口
**核心功能**：
- 用户头像、昵称、认证状态展示
- 个人信息管理入口
- 证书管理入口
- 系统功能入口（投诉建议、关于我们）
**交互说明**：
- 顶部展示微信头像、昵称和认证状态标识
- 功能列表根据用户状态动态显示相应入口
- 非正式用户仅显示有权限的功能入口
- 正式用户可访问完整功能列表

#### `个人信息详情页面`
**用途**：展示用户的完整个人信息（正式用户）
**核心功能**：
- 个人信息展示（脱敏处理）
- 隶属机构和职位信息
- 证书有效期显示
**交互说明**：
- 只读展示，不提供修改功能
- 敏感信息（手机号、身份证）进行脱敏处理
- 清晰显示证书有效期信息

#### `证书管理页面`
**用途**：管理用户的电子证书（正式用户）
**核心功能**：
- 当前证书状态展示
- 证书图片查看和保存
- 历史证书列表
**交互说明**：
- 根据证书状态显示不同的信息区域
- 新证审批中且旧证有效时同时显示两者信息
- 证书图片支持查看大图和长按保存
- 历史证书列表显示已失效证书的基本信息

#### `投诉与建议页面`
**用途**：用户反馈意见和问题的简化提交界面
**核心功能**：
- 反馈内容输入
- 反馈提交
**交互说明**：
- 提供大的多行文本输入框
- 可设置字数限制和必填校验
- 提交成功后给出友好提示

#### `关于我们页面`
**用途**：展示应用的基本信息
**核心功能**：
- 应用Logo和名称
- 版本号信息
- 机构介绍
- 联系方式和版权信息
**交互说明**：
- 静态内容展示
- 内容可通过后台配置或硬编码
- 简洁清晰的信息布局

### 4.2 页面跳转关系

```
登录页 → 个人资料提交页 → 信息中心页（主入口）
     ↘                  ↗
      跳过提交 ────────────

信息中心页 ⟷ 信息详情页

学习中心页 → 题库分类选择页 → 题库练习页 → 练习总结页
                                    ↺ 
                               (循环练习)

考试中心页 → 线上考试-考前阅读页 → 线上考试-人脸识别页 → 线上考试-答题页
         → 线下考试详情页
         → 历史考试记录页

个人中心页 → 个人信息详情页
         → 证书管理页
         → 投诉与建议页
         → 关于我们页
```

## 5. 技术特性

### 5.1 防作弊机制
- **人脸识别**：考前身份验证 + 考中随机抓拍
- **切屏监控**：记录切屏次数和时长
- **防复制**：题目区域禁止复制粘贴
- **水印保护**：界面叠加考生信息水印
- **录音监控**：预留接口（需用户授权）

### 5.2 性能优化
- **分包加载**：主包控制在1.8MB以内
- **懒加载**：图片和非首屏组件延迟加载
- **缓存机制**：静态数据本地缓存
- **加载提示**：完善的加载状态反馈

### 5.3 用户体验
- **清新设计**：柔和渐变配色，呼吸感留白
- **响应式**：适配不同设备尺寸
- **交互反馈**：操作即时反馈
- **错误处理**：友好的错误提示和引导

## 6. 设计规范

### 6.1 UI设计风格
- **优雅清新**：清新主义美学与功能的完美平衡
- **柔和配色**：蓝绿色系，符合疾控机构专业形象
- **层级清晰**：信息层级通过阴影和卡片布局清晰呈现
- **细节精致**：精心打磨的圆角和微交互动效

### 6.2 技术规范
- **组件标准**：优先使用uview-plus组件库
- **样式隔离**：所有页面使用scoped样式
- **响应式单位**：布局优先使用rpx单位
- **代码规范**：TypeScript + ESLint + Prettier

## 7. 接下来的工作

基于以上产品设计和交互设计，接下来将进行：
1. **UI设计与代码生成**：为每个页面创建.vue单文件组件
2. **项目配置**：生成pages.json等配置文件
3. **代码规范检查**：确保代码质量和规范性

---

**文档状态**：已完成产品功能设计和交互设计  
**下一步**：进入UI设计与代码生成阶段 