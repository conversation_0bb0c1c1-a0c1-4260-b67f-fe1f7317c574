<template>
  <view class="about-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="关于我们" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 应用信息 -->
    <view class="app-info-section">
      <view class="app-logo">
        <image src="/static/images/logo.png" class="logo-image" mode="aspectFit" />
      </view>
      <view class="app-details">
        <text class="app-name">疾控医护考试系统</text>
        <text class="app-version">版本 {{ appVersion }}</text>
        <text class="app-description">专业的疾控医护人员学习考试平台</text>
      </view>
    </view>
    
    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="section-header">
        <u-icon name="star" color="#2E8B57" size="32" />
        <text class="section-title">核心功能</text>
      </view>
      <view class="features-list">
        <view class="feature-item">
          <view class="feature-icon">
            <u-icon name="book" color="#4A90E2" size="40" />
          </view>
          <view class="feature-content">
            <text class="feature-title">在线学习</text>
            <text class="feature-desc">丰富的题库资源，支持多种题型练习</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <u-icon name="edit-pen" color="#4CAF50" size="40" />
          </view>
          <view class="feature-content">
            <text class="feature-title">在线考试</text>
            <text class="feature-desc">人脸识别验证，防作弊监控系统</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <u-icon name="medal" color="#FFD700" size="40" />
          </view>
          <view class="feature-content">
            <text class="feature-title">证书管理</text>
            <text class="feature-desc">电子证书生成，支持下载和分享</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <u-icon name="chart" color="#FF9500" size="40" />
          </view>
          <view class="feature-content">
            <text class="feature-title">学习统计</text>
            <text class="feature-desc">详细的学习数据分析和进度跟踪</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 技术支持 -->
    <view class="support-section">
      <view class="section-header">
        <u-icon name="headphones" color="#2E8B57" size="32" />
        <text class="section-title">技术支持</text>
      </view>
      <view class="support-list">
        <view class="support-item" @click="makePhoneCall">
          <view class="support-icon">
            <u-icon name="phone" color="#4CAF50" size="32" />
          </view>
          <view class="support-content">
            <text class="support-title">客服热线</text>
            <text class="support-value">************</text>
          </view>
          <u-icon name="arrow-right" color="#c0c4cc" size="24" />
        </view>
        
        <view class="support-item" @click="copyEmail">
          <view class="support-icon">
            <u-icon name="email" color="#4A90E2" size="32" />
          </view>
          <view class="support-content">
            <text class="support-title">邮箱支持</text>
            <text class="support-value"><EMAIL></text>
          </view>
          <u-icon name="arrow-right" color="#c0c4cc" size="24" />
        </view>
        
        <view class="support-item">
          <view class="support-icon">
            <u-icon name="clock" color="#FF9500" size="32" />
          </view>
          <view class="support-content">
            <text class="support-title">服务时间</text>
            <text class="support-value">工作日 9:00-18:00</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 开发团队 -->
    <view class="team-section">
      <view class="section-header">
        <u-icon name="account-circle" color="#2E8B57" size="32" />
        <text class="section-title">开发团队</text>
      </view>
      <view class="team-content">
        <text class="team-description">
          本系统由专业的技术团队开发，采用现代化的技术架构，确保系统的稳定性和安全性。
          我们致力于为疾控医护人员提供优质的学习考试服务。
        </text>
        <view class="tech-stack">
          <text class="tech-title">技术栈：</text>
          <view class="tech-tags">
            <view class="tech-tag">Vue3</view>
            <view class="tech-tag">TypeScript</view>
            <view class="tech-tag">uni-app</view>
            <view class="tech-tag">uview-plus</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 法律信息 -->
    <view class="legal-section">
      <view class="section-header">
        <u-icon name="shield" color="#2E8B57" size="32" />
        <text class="section-title">法律信息</text>
      </view>
      <view class="legal-list">
        <view class="legal-item" @click="viewPrivacyPolicy">
          <text class="legal-title">隐私政策</text>
          <u-icon name="arrow-right" color="#c0c4cc" size="24" />
        </view>
        <view class="legal-item" @click="viewUserAgreement">
          <text class="legal-title">用户协议</text>
          <u-icon name="arrow-right" color="#c0c4cc" size="24" />
        </view>
        <view class="legal-item">
          <text class="legal-title">版权所有</text>
          <text class="legal-value">© 2024 疾控中心</text>
        </view>
      </view>
    </view>
    
    <!-- 版本更新 -->
    <view class="update-section">
      <view class="section-header">
        <u-icon name="refresh" color="#2E8B57" size="32" />
        <text class="section-title">版本信息</text>
      </view>
      <view class="update-content">
        <view class="version-info">
          <view class="version-item">
            <text class="version-label">当前版本：</text>
            <text class="version-value">{{ appVersion }}</text>
          </view>
          <view class="version-item">
            <text class="version-label">更新时间：</text>
            <text class="version-value">{{ updateTime }}</text>
          </view>
        </view>
        
        <u-button 
          class="check-update-btn"
          type="primary"
          plain
          size="medium"
          @click="checkUpdate"
        >
          检查更新
        </u-button>
      </view>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer-info">
      <text class="footer-text">感谢您使用疾控医护考试系统</text>
      <text class="footer-text">让学习更高效，让考试更便捷</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAppStore } from '../../../src/stores/app'

// Store
const appStore = useAppStore()

// 应用信息
const appVersion = ref('1.0.0')
const updateTime = ref('2024-12-01')

// 拨打客服电话
const makePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: '************',
    fail: (error) => {
      console.error('拨打电话失败:', error)
      appStore.showToast('拨打电话失败')
    }
  })
}

// 复制邮箱
const copyEmail = () => {
  uni.setClipboardData({
    data: '<EMAIL>',
    success: () => {
      appStore.showToast('邮箱地址已复制', 'success')
    },
    fail: (error) => {
      console.error('复制失败:', error)
      appStore.showToast('复制失败')
    }
  })
}

// 查看隐私政策
const viewPrivacyPolicy = () => {
  appStore.showModal({
    title: '隐私政策',
    content: '我们非常重视您的隐私保护。本应用仅收集必要的用户信息用于提供服务，不会泄露给第三方。',
    showCancelButton: false,
    confirmText: '我知道了'
  })
}

// 查看用户协议
const viewUserAgreement = () => {
  appStore.showModal({
    title: '用户协议',
    content: '使用本应用即表示您同意遵守相关使用条款。请合理使用系统功能，不得进行违法违规操作。',
    showCancelButton: false,
    confirmText: '我知道了'
  })
}

// 检查更新
const checkUpdate = () => {
  appStore.showLoading('检查中...')
  
  // 模拟检查更新
  setTimeout(() => {
    appStore.hideLoading()
    appStore.showToast('当前已是最新版本', 'success')
  }, 2000)
}
</script>

<style lang="scss" scoped>
@import '../../../src/styles/global.scss';

.about-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 40rpx;
}

.app-info-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .app-logo {
    margin-bottom: 32rpx;
    
    .logo-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 24rpx;
    }
  }
  
  .app-details {
    .app-name {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 12rpx;
    }
    
    .app-version {
      display: block;
      font-size: 26rpx;
      color: #2E8B57;
      margin-bottom: 16rpx;
    }
    
    .app-description {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}

.features-section,
.support-section,
.team-section,
.legal-section,
.update-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    .section-title {
      margin-left: 16rpx;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.features-list {
  .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .feature-icon {
      width: 80rpx;
      height: 80rpx;
      background: rgba(74, 144, 226, 0.1);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
    }
    
    .feature-content {
      flex: 1;
      
      .feature-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .feature-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}

.support-list {
  .support-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #f8f9fa;
    
    &:last-child {
      border-bottom: none;
    }
    
    .support-icon {
      margin-right: 24rpx;
    }
    
    .support-content {
      flex: 1;
      
      .support-title {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 4rpx;
      }
      
      .support-value {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.team-content {
  .team-description {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 32rpx;
  }
  
  .tech-stack {
    .tech-title {
      font-size: 26rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 16rpx;
    }
    
    .tech-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      
      .tech-tag {
        padding: 8rpx 16rpx;
        background: #e3f2fd;
        color: #2E8B57;
        border-radius: 12rpx;
        font-size: 22rpx;
        font-weight: bold;
      }
    }
  }
}

.legal-list {
  .legal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #f8f9fa;
    
    &:last-child {
      border-bottom: none;
    }
    
    .legal-title {
      font-size: 28rpx;
      color: #333;
    }
    
    .legal-value {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.update-content {
  .version-info {
    margin-bottom: 32rpx;
    
    .version-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .version-label {
        font-size: 26rpx;
        color: #666;
        min-width: 140rpx;
      }
      
      .version-value {
        font-size: 26rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
  
  .check-update-btn {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
  }
}

.footer-info {
  text-align: center;
  padding: 40rpx;
  
  .footer-text {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
