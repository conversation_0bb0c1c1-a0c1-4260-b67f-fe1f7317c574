import App from './App'
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
// 引入uview-plus
import uviewPlus from './uni_modules/uview-plus/index.js'

export function createApp() {
	const app = createSSRApp(App)

	// 使用 Pinia 状态管理
	const pinia = createPinia()
	app.use(pinia)

	// 使用 uview-plus UI库
	app.use(uviewPlus)

	// 全局配置
	app.config.globalProperties.$appName = '疾控医护考试系统'
	app.config.globalProperties.$version = '1.0.0'

	return {
		app,
		Pinia: createPinia
	}
}
