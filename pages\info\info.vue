<template>
  <view class="info-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">信息中心</text>
        <view class="header-actions">
          <u-icon name="search" color="#fff" size="44" @click="handleSearch" />
        </view>
      </view>
    </view>
    
    <!-- 用户状态提示 -->
    <UserStatusBanner :showAction="true" />
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 轮播公告 -->
      <view v-if="bannerList.length > 0" class="banner-section">
        <swiper 
          class="banner-swiper" 
          :indicator-dots="bannerList.length > 1"
          :autoplay="true"
          :interval="5000"
          :duration="500"
          circular
        >
          <swiper-item v-for="banner in bannerList" :key="banner.id">
            <view class="banner-item" @click="viewDetail(banner)">
              <view class="banner-content">
                <text class="banner-title">{{ banner.title }}</text>
                <text class="banner-desc">{{ banner.content.substring(0, 50) }}...</text>
              </view>
              <view v-if="banner.isImportant" class="banner-tag important">重要</view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 内容分类 -->
      <view class="content-categories">
        <u-tabs 
          v-model="currentTab" 
          :list="tabList" 
          @change="onTabChange"
          activeColor="#2E8B57"
          inactiveColor="#666"
          lineColor="#2E8B57"
          :lineWidth="60"
          :lineHeight="6"
        />
      </view>
      
      <!-- 内容列表 -->
      <view class="content-list">
        <scroll-view 
          class="list-scroll"
          scroll-y
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="onLoadMore"
        >
          <!-- 空状态 -->
          <EmptyState 
            v-if="currentList.length === 0 && !isLoading"
            type="no-data"
            title="暂无内容"
            description="当前分类下暂时没有内容"
            :showButton="true"
            buttonText="刷新"
            @buttonClick="onRefresh"
          />
          
          <!-- 内容列表 -->
          <view v-else>
            <view 
              v-for="item in currentList" 
              :key="item.id" 
              class="content-item"
              @click="viewDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
                <view v-if="item.isImportant" class="important-tag">
                  <text>重要</text>
                </view>
                <view v-if="item.isTop" class="top-tag">
                  <text>置顶</text>
                </view>
              </view>
              
              <text v-if="item.content" class="item-summary">{{ item.content.substring(0, 100) }}...</text>
              
              <view class="item-footer">
                <text class="item-date">{{ formatRelativeTime(item.publishTime) }}</text>
                <view class="item-meta">
                  <text class="item-source" v-if="item.source">{{ item.source }}</text>
                  <StatusTag :type="'announcement'" :status="item.type" />
                </view>
              </view>
            </view>
          </view>
          
          <!-- 加载更多提示 -->
          <view v-if="isLoading" class="loading-more">
            <LoadingSpinner size="small" text="加载中..." />
          </view>
          
          <view v-if="!hasMore && currentList.length > 0" class="no-more">
            <text>没有更多内容了</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../../src/stores/user'
import { useAppStore } from '../../src/stores/app'
import api from '../../src/api'
import { PAGE_PATHS } from '../../src/constants'
import { formatRelativeTime } from '../../src/utils'
import type { Announcement, PaginationParams } from '../../src/types'

// 导入组件
import UserStatusBanner from '../../src/components/common/UserStatusBanner.vue'
import EmptyState from '../../src/components/common/EmptyState.vue'
import LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'
import StatusTag from '../../src/components/common/StatusTag.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 系统信息
const statusBarHeight = ref(0)

// 数据状态
const currentTab = ref(0)
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)

// 数据列表
const bannerList = ref<Announcement[]>([])
const allList = ref<Announcement[]>([])

// Tab配置
const tabList = [
  { name: '全部', key: 'all' },
  { name: '公告', key: 'notice' },
  { name: '政策法规', key: 'policy' },
  { name: '重要通知', key: 'news' }
]

// 计算当前列表
const currentList = computed(() => {
  const currentTabKey = tabList[currentTab.value].key
  if (currentTabKey === 'all') {
    return allList.value
  }
  return allList.value.filter(item => item.type === currentTabKey)
})

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 初始化数据
  loadData()
  loadBanners()
})

// 切换Tab
const onTabChange = (index: number) => {
  currentTab.value = index
  currentPage.value = 1
  hasMore.value = true
  allList.value = []
  loadData()
}

// 加载轮播数据
const loadBanners = async () => {
  try {
    const response = await api.info.getAnnouncements({
      page: 1,
      pageSize: 3,
      type: 'notice'
    })
    
    bannerList.value = response.data.list.filter(item => item.isImportant)
  } catch (error) {
    console.error('加载轮播数据失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    const params: PaginationParams & { type?: string } = {
      page: currentPage.value,
      pageSize: 10
    }
    
    const currentTabKey = tabList[currentTab.value].key
    if (currentTabKey !== 'all') {
      params.type = currentTabKey
    }
    
    const response = await api.info.getAnnouncements(params)
    
    if (currentPage.value === 1) {
      allList.value = response.data.list
    } else {
      allList.value.push(...response.data.list)
    }
    
    hasMore.value = response.data.page < response.data.totalPages
  } catch (error: any) {
    console.error('加载数据失败:', error)
    appStore.showToast(error.message || '加载失败，请重试')
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  allList.value = []
  loadData()
  loadBanners()
}

// 加载更多
const onLoadMore = () => {
  if (hasMore.value && !isLoading.value) {
    currentPage.value++
    loadData()
  }
}

// 查看详情
const viewDetail = (item: Announcement) => {
  appStore.navigateTo(PAGE_PATHS.INFO_DETAIL, { id: item.id })
}

// 搜索
const handleSearch = () => {
  appStore.showToast('搜索功能开发中')
}
</script>

<style lang="scss" scoped>
@import '../../src/styles/global.scss';

.info-center-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.status-bar {
  background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-dark 100%);
}

.header {
  background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-dark 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
}

.main-content {
  padding-bottom: 120rpx; // 为底部导航留空间
}

.banner-section {
  margin: 30rpx;
  
  .banner-swiper {
    height: 200rpx;
    border-radius: 24rpx;
    overflow: hidden;
    
    .banner-item {
      position: relative;
      height: 100%;
      background: linear-gradient(135deg, $acdc-primary 0%, $acdc-primary-light 100%);
      padding: 40rpx;
      display: flex;
      align-items: center;
      
      .banner-content {
        flex: 1;
        
        .banner-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #fff;
          margin-bottom: 16rpx;
          line-height: 1.4;
        }
        
        .banner-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.4;
        }
      }
      
      .banner-tag {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 8rpx 16rpx;
        background: rgba(255, 149, 0, 0.9);
        border-radius: 12rpx;
        font-size: 20rpx;
        color: #fff;
        
        &.important {
          background: rgba(245, 34, 45, 0.9);
        }
      }
    }
  }
}

.content-categories {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.content-list {
  .list-scroll {
    height: calc(100vh - 400rpx);
  }
  
  .content-item {
    margin: 20rpx 30rpx;
    padding: 40rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20rpx;
      gap: 16rpx;
      
      .item-title {
        flex: 1;
        font-size: 30rpx;
        font-weight: bold;
        color: $acdc-text-primary;
        line-height: 1.4;
      }
      
      .important-tag {
        padding: 8rpx 16rpx;
        background: #fff3e0;
        border-radius: 12rpx;
        border: 2rpx solid $uni-color-warning;
        
        text {
          font-size: 20rpx;
          color: $uni-color-warning;
          font-weight: bold;
        }
      }
      
      .top-tag {
        padding: 8rpx 16rpx;
        background: #f6ffed;
        border-radius: 12rpx;
        border: 2rpx solid $uni-color-success;
        
        text {
          font-size: 20rpx;
          color: $uni-color-success;
          font-weight: bold;
        }
      }
    }
    
    .item-summary {
      display: block;
      font-size: 26rpx;
      color: $acdc-text-secondary;
      line-height: 1.5;
      margin-bottom: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .item-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .item-date {
        font-size: 24rpx;
        color: $acdc-text-disabled;
      }
      
      .item-meta {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .item-source {
          font-size: 24rpx;
          color: $acdc-text-disabled;
        }
      }
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: $acdc-text-disabled;
  font-size: 26rpx;
}
</style>
