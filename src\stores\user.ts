import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserStatus } from '../types'
import { STORAGE_KEYS } from '../constants'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<User | null>(null)
  const isLoggedIn = ref<boolean>(false)

  // 计算属性
  const isAuthenticated = computed(() => {
    return isLoggedIn.value && userInfo.value?.status === 'approved'
  })

  const userStatusText = computed(() => {
    if (!userInfo.value) return '未登录'
    
    const statusMap = {
      not_submitted: '未提交资料',
      pending: '审核中',
      approved: '已认证',
      rejected: '审核未通过'
    }
    
    return statusMap[userInfo.value.status] || '未知状态'
  })

  const canAccessExam = computed(() => {
    return userInfo.value?.status === 'approved'
  })

  const canAccessFullFeatures = computed(() => {
    return userInfo.value?.status === 'approved'
  })

  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    isLoggedIn.value = !!newToken
    
    // 持久化存储
    if (newToken) {
      uni.setStorageSync(STORAGE_KEYS.TOKEN, newToken)
    } else {
      uni.removeStorageSync(STORAGE_KEYS.TOKEN)
    }
  }

  const setUserInfo = (user: User | null) => {
    userInfo.value = user
    
    // 持久化存储
    if (user) {
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(user))
    } else {
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
    }
  }

  const updateUserStatus = (status: UserStatus) => {
    if (userInfo.value) {
      userInfo.value.status = status
      setUserInfo(userInfo.value)
    }
  }

  const login = (tokenValue: string, user: User) => {
    setToken(tokenValue)
    setUserInfo(user)
  }

  const logout = () => {
    setToken('')
    setUserInfo(null)
    
    // 清除其他相关缓存
    uni.removeStorageSync(STORAGE_KEYS.PRACTICE_CACHE)
    uni.removeStorageSync(STORAGE_KEYS.EXAM_CACHE)
  }

  const initFromStorage = () => {
    try {
      // 恢复token
      const storedToken = uni.getStorageSync(STORAGE_KEYS.TOKEN)
      if (storedToken) {
        token.value = storedToken
        isLoggedIn.value = true
      }

      // 恢复用户信息
      const storedUserInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO)
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo)
      }
    } catch (error) {
      console.error('Failed to init user store from storage:', error)
      // 清除可能损坏的数据
      logout()
    }
  }

  const updateProfile = (updates: Partial<User>) => {
    if (userInfo.value) {
      const updatedUser = { ...userInfo.value, ...updates }
      setUserInfo(updatedUser)
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    
    // 计算属性
    isAuthenticated,
    userStatusText,
    canAccessExam,
    canAccessFullFeatures,
    
    // 方法
    setToken,
    setUserInfo,
    updateUserStatus,
    login,
    logout,
    initFromStorage,
    updateProfile,
  }
})
