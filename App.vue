<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from './src/stores/user'
import { useAppStore } from './src/stores/app'
import { useStudyStore } from './src/stores/study'
import { initRouterGuard } from './src/utils/router-guard'

// Store
const userStore = useUserStore()
const appStore = useAppStore()
const studyStore = useStudyStore()

onLaunch(() => {
  console.log('疾控医护考试系统启动')

  // 初始化应用
  initApp()
})

onShow(() => {
  console.log('App Show')

  // 检查网络状态
  appStore.checkNetworkStatus()
})

onHide(() => {
  console.log('App Hide')
})

// 初始化应用
const initApp = async () => {
  try {
    // 初始化应用状态
    await appStore.initApp()

    // 从本地存储恢复用户状态
    userStore.initFromStorage()

    // 初始化学习状态
    studyStore.initStudyStore()

    // 初始化路由守卫
    initRouterGuard()

    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
}
</script>

<style lang="scss">
// 引入uni.scss变量（包含uview-plus主题变量）
@import 'uni.scss';

// 引入uview-plus样式
@import 'uni_modules/uview-plus/index.scss';

// 引入全局样式
@import 'src/styles/global.scss';

/* 全局页面样式 */
page {
  background-color: #F8F9FA;
  height: 100%;
  font-size: 28rpx;
  line-height: 1.6;
  color: #262626;
}

/* H5 兼容样式 */
/* #ifdef H5 */
@media screen and (min-width: 768px) {
  body {
    overflow-y: scroll;
  }
}

uni-page-body {
  background-color: #F8F9FA !important;
  min-height: 100% !important;
  height: auto !important;
}
/* #endif */

/* 通用样式重置 */
* {
  box-sizing: border-box;
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 禁用长按选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
