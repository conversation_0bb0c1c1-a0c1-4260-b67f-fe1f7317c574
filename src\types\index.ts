// 用户相关类型定义
export interface User {
  id: string
  openId: string
  unionId?: string
  nickname: string
  avatar: string
  realName?: string
  phone?: string
  idCard?: string
  organization?: string
  position?: string
  photo?: string
  status: UserStatus
  certificateExpiry?: string
  createdAt: string
  updatedAt: string
}

export enum UserStatus {
  NOT_SUBMITTED = 'not_submitted', // 未提交资料
  PENDING = 'pending', // 待审核
  APPROVED = 'approved', // 已通过
  REJECTED = 'rejected', // 审核未通过
}

// 信息中心相关类型
export interface Announcement {
  id: string
  title: string
  content: string
  type: AnnouncementType
  isTop: boolean
  isImportant: boolean
  publishTime: string
  source?: string
}

export enum AnnouncementType {
  NOTICE = 'notice', // 公告
  POLICY = 'policy', // 政策法规
  NEWS = 'news', // 重要通知
}

// 学习中心相关类型
export interface QuestionCategory {
  id: string
  name: string
  description?: string
  questionCount: number
  icon?: string
}

export interface Question {
  id: string
  categoryId: string
  type: QuestionType
  title: string
  options?: string[]
  answer: string | string[]
  explanation?: string
  difficulty: QuestionDifficulty
}

export enum QuestionType {
  SINGLE = 'single', // 单选
  MULTIPLE = 'multiple', // 多选
  JUDGE = 'judge', // 判断
  ESSAY = 'essay', // 问答
}

export enum QuestionDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

export interface PracticeSession {
  id: string
  categoryId: string
  questions: Question[]
  answers: Record<string, any>
  score?: number
  correctCount?: number
  totalCount: number
  startTime: string
  endTime?: string
}

// 考试相关类型
export interface Exam {
  id: string
  name: string
  type: ExamType
  status: ExamStatus
  description?: string
  startTime: string
  endTime: string
  duration: number // 分钟
  totalScore: number
  passScore: number
  allowRetake: boolean
  maxRetakeCount?: number
}

export enum ExamType {
  ONLINE = 'online', // 线上考试
  OFFLINE = 'offline', // 线下考试
}

export enum ExamStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
}

export interface ExamRecord {
  id: string
  examId: string
  userId: string
  score?: number
  status: ExamRecordStatus
  startTime: string
  endTime?: string
  answers?: Record<string, any>
  retakeCount: number
}

export enum ExamRecordStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  SUBMITTED = 'submitted',
  PASSED = 'passed',
  FAILED = 'failed',
}

// 线下考试相关
export interface ExamVenue {
  id: string
  name: string
  address: string
  capacity: number
  schedules: ExamSchedule[]
}

export interface ExamSchedule {
  id: string
  venueId: string
  examId: string
  date: string
  startTime: string
  endTime: string
  capacity: number
  registeredCount: number
}

export interface ExamRegistration {
  id: string
  userId: string
  scheduleId: string
  status: RegistrationStatus
  registeredAt: string
}

export enum RegistrationStatus {
  REGISTERED = 'registered',
  CANCELLED = 'cancelled',
  ATTENDED = 'attended',
  ABSENT = 'absent',
}

// 证书相关类型
export interface Certificate {
  id: string
  userId: string
  name: string
  issueDate: string
  expiryDate: string
  status: CertificateStatus
  imageUrl?: string
  downloadUrl?: string
}

export enum CertificateStatus {
  PENDING = 'pending', // 审批中
  ACTIVE = 'active', // 有效
  EXPIRED = 'expired', // 已过期
  REVOKED = 'revoked', // 已撤销
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

export interface PaginationParams {
  page: number
  pageSize: number
}

export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用类型
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

export interface UploadFile {
  name: string
  url: string
  size: number
  type: string
}
