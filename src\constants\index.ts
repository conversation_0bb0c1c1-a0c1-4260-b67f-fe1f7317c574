// API 基础配置
export const API_CONFIG = {
  BASE_URL: 'https://api.acdc-exam.com',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
}

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'acdc_token',
  USER_INFO: 'acdc_user_info',
  PRACTICE_CACHE: 'acdc_practice_cache',
  EXAM_CACHE: 'acdc_exam_cache',
  SETTINGS: 'acdc_settings',
}

// 用户状态文本映射
export const USER_STATUS_TEXT = {
  not_submitted: '未提交资料',
  pending: '审核中',
  approved: '已认证',
  rejected: '审核未通过',
}

// 考试状态文本映射
export const EXAM_STATUS_TEXT = {
  not_started: '未开始',
  in_progress: '进行中',
  completed: '已完成',
  expired: '已过期',
}

// 考试记录状态文本映射
export const EXAM_RECORD_STATUS_TEXT = {
  not_started: '未开始',
  in_progress: '答题中',
  submitted: '已提交',
  passed: '已通过',
  failed: '未通过',
}

// 证书状态文本映射
export const CERTIFICATE_STATUS_TEXT = {
  pending: '审批中',
  active: '有效',
  expired: '已过期',
  revoked: '已撤销',
}

// 题目类型文本映射
export const QUESTION_TYPE_TEXT = {
  single: '单选题',
  multiple: '多选题',
  judge: '判断题',
  essay: '问答题',
}

// 公告类型文本映射
export const ANNOUNCEMENT_TYPE_TEXT = {
  notice: '公告',
  policy: '政策法规',
  news: '重要通知',
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_SIZE: 200 * 1024, // 200KB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/jpg'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png'],
}

// 练习配置
export const PRACTICE_CONFIG = {
  QUESTIONS_PER_SESSION: 10, // 每组题目数量
  FREE_SESSIONS_PER_DAY: 3, // 免费用户每日练习次数
  SESSION_TIMEOUT: 30 * 60 * 1000, // 练习超时时间（毫秒）
}

// 考试配置
export const EXAM_CONFIG = {
  FACE_VERIFY_MAX_RETRY: 3, // 人脸识别最大重试次数
  AUTO_SUBMIT_BEFORE_END: 30, // 考试结束前自动提交时间（秒）
  ANTI_CHEAT_CHECK_INTERVAL: 10000, // 防作弊检查间隔（毫秒）
  MAX_SWITCH_COUNT: 5, // 最大切屏次数
}

// 页面路径
export const PAGE_PATHS = {
  LOGIN: '/pages/login/login',
  PROFILE: '/pages/profile/profile',
  INFO: '/pages/info/info',
  INFO_DETAIL: '/pages/info/detail',
  STUDY: '/pages/study/study',
  STUDY_CATEGORY: '/pages/study/category',
  STUDY_PRACTICE: '/pages/study/practice',
  STUDY_SUMMARY: '/pages/study/summary',
  EXAM: '/pages/exam/exam',
  PERSONAL: '/pages/personal/personal',
  
  // 分包页面
  EXAM_ONLINE_READING: '/subpages/exam/online/reading',
  EXAM_ONLINE_FACE_VERIFY: '/subpages/exam/online/face-verify',
  EXAM_ONLINE_ANSWER: '/subpages/exam/online/answer',
  EXAM_OFFLINE_DETAIL: '/subpages/exam/offline/detail',
  EXAM_HISTORY: '/subpages/exam/history/history',
  
  PERSONAL_INFO: '/subpages/personal/info/info',
  PERSONAL_CERTIFICATE: '/subpages/personal/certificate/certificate',
  PERSONAL_FEEDBACK: '/subpages/personal/feedback/feedback',
  PERSONAL_ABOUT: '/subpages/personal/about/about',
}

// 主题色彩
export const THEME_COLORS = {
  PRIMARY: '#2E8B57', // 海绿色 - 主色调
  PRIMARY_LIGHT: '#3CB371', // 中海绿色
  PRIMARY_DARK: '#228B22', // 森林绿
  
  SUCCESS: '#52C41A', // 成功色
  WARNING: '#FAAD14', // 警告色
  ERROR: '#F5222D', // 错误色
  INFO: '#1890FF', // 信息色
  
  TEXT_PRIMARY: '#262626', // 主要文字
  TEXT_SECONDARY: '#595959', // 次要文字
  TEXT_DISABLED: '#BFBFBF', // 禁用文字
  
  BACKGROUND: '#F8F9FA', // 背景色
  BACKGROUND_LIGHT: '#FFFFFF', // 浅背景色
  BORDER: '#E8E8E8', // 边框色
  DIVIDER: '#F0F0F0', // 分割线色
}

// 错误码映射
export const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  NETWORK_ERROR: -1,
  TIMEOUT: -2,
}

// 错误信息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.UNAUTHORIZED]: '登录已过期，请重新登录',
  [ERROR_CODES.FORBIDDEN]: '没有权限访问',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.SERVER_ERROR]: '服务器错误，请稍后重试',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络',
  [ERROR_CODES.TIMEOUT]: '请求超时，请重试',
}

// 正则表达式
export const REGEX = {
  PHONE: /^1[3-9]\d{9}$/, // 手机号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, // 身份证号
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // 邮箱
}

// 默认头像
export const DEFAULT_AVATAR = '/static/images/default-avatar.png'

// 空状态图片
export const EMPTY_IMAGES = {
  NO_DATA: '/static/images/empty-data.png',
  NO_NETWORK: '/static/images/empty-network.png',
  NO_PERMISSION: '/static/images/empty-permission.png',
}
