import request from '../utils/request'
import type {
  User,
  Announcement,
  QuestionCategory,
  Question,
  Exam,
  ExamRecord,
  ExamVenue,
  Certificate,
  PaginationParams,
  PaginationResponse,
  ApiResponse
} from '../types'

// 用户相关API
export const userApi = {
  // 微信登录
  wxLogin: (data: { code: string; userInfo: any }) => 
    request.post<{ token: string; user: User }>('/auth/wx-login', data),

  // 提交个人资料
  submitProfile: (data: FormData) => 
    request.post<User>('/user/profile', data),

  // 获取用户信息
  getUserInfo: () => 
    request.get<User>('/user/info'),

  // 更新用户信息
  updateUserInfo: (data: Partial<User>) => 
    request.put<User>('/user/info', data),

  // 上传头像
  uploadAvatar: (filePath: string) => 
    request.upload({
      url: '/user/avatar',
      filePath,
      name: 'avatar',
    }),

  // 上传本人照片
  uploadPhoto: (filePath: string) => 
    request.upload({
      url: '/user/photo',
      filePath,
      name: 'photo',
    }),
}

// 信息中心API
export const infoApi = {
  // 获取公告列表
  getAnnouncements: (params: PaginationParams & { type?: string }) => 
    request.get<PaginationResponse<Announcement>>('/announcements', params),

  // 获取公告详情
  getAnnouncementDetail: (id: string) => 
    request.get<Announcement>(`/announcements/${id}`),

  // 获取政策法规列表
  getPolicies: (params: PaginationParams) => 
    request.get<PaginationResponse<Announcement>>('/policies', params),

  // 获取重要通知列表
  getNotices: (params: PaginationParams) => 
    request.get<PaginationResponse<Announcement>>('/notices', params),
}

// 学习中心API
export const studyApi = {
  // 获取题库分类
  getCategories: () => 
    request.get<QuestionCategory[]>('/study/categories'),

  // 获取题目列表
  getQuestions: (categoryId: string, count: number = 10) => 
    request.get<Question[]>('/study/questions', { categoryId, count }),

  // 提交练习结果
  submitPractice: (data: {
    categoryId: string
    questions: string[]
    answers: Record<string, any>
    score: number
    correctCount: number
  }) => 
    request.post('/study/practice', data),

  // 获取练习历史
  getPracticeHistory: (params: PaginationParams) => 
    request.get<PaginationResponse<any>>('/study/practice/history', params),

  // 获取学习统计
  getStudyStats: () => 
    request.get<{
      totalSessions: number
      totalQuestions: number
      totalCorrect: number
      averageScore: number
      accuracy: number
    }>('/study/stats'),
}

// 考试中心API
export const examApi = {
  // 获取当前考试列表
  getCurrentExams: () => 
    request.get<Exam[]>('/exams/current'),

  // 获取考试详情
  getExamDetail: (id: string) => 
    request.get<Exam>(`/exams/${id}`),

  // 获取考试题目
  getExamQuestions: (examId: string) => 
    request.get<Question[]>(`/exams/${examId}/questions`),

  // 开始考试
  startExam: (examId: string) => 
    request.post<{ recordId: string }>(`/exams/${examId}/start`),

  // 人脸识别验证
  verifyFace: (data: { examId: string; photo: string }) => 
    request.post<{ success: boolean; similarity: number }>('/exams/verify-face', data),

  // 提交答案
  submitAnswers: (data: {
    recordId: string
    answers: Record<string, any>
    duration: number
    antiCheatLogs?: any[]
  }) => 
    request.post('/exams/submit', data),

  // 获取考试记录
  getExamRecords: (params: PaginationParams) => 
    request.get<PaginationResponse<ExamRecord>>('/exams/records', params),

  // 获取线下考试场地
  getExamVenues: (examId: string) => 
    request.get<ExamVenue[]>(`/exams/${examId}/venues`),

  // 预约线下考试
  bookOfflineExam: (data: { scheduleId: string }) => 
    request.post('/exams/book', data),

  // 取消预约
  cancelBooking: (registrationId: string) => 
    request.delete(`/exams/booking/${registrationId}`),

  // 上传防作弊日志
  uploadAntiCheatLog: (data: {
    recordId: string
    type: string
    timestamp: string
    data: any
  }) => 
    request.post('/exams/anti-cheat-log', data),
}

// 个人中心API
export const personalApi = {
  // 获取证书列表
  getCertificates: () => 
    request.get<Certificate[]>('/certificates'),

  // 获取证书详情
  getCertificateDetail: (id: string) => 
    request.get<Certificate>(`/certificates/${id}`),

  // 下载证书
  downloadCertificate: (id: string) => 
    request.get<{ url: string }>(`/certificates/${id}/download`),

  // 提交反馈
  submitFeedback: (data: { content: string }) => 
    request.post('/feedback', data),

  // 获取关于我们信息
  getAboutInfo: () => 
    request.get<{
      appName: string
      version: string
      description: string
      contact: string
      copyright: string
    }>('/about'),
}

// 通用API
export const commonApi = {
  // 获取机构列表
  getOrganizations: (keyword?: string) => 
    request.get<Array<{ id: string; name: string }>>('/common/organizations', { keyword }),

  // 获取职位列表
  getPositions: (keyword?: string) => 
    request.get<Array<{ id: string; name: string }>>('/common/positions', { keyword }),

  // 上传文件
  uploadFile: (filePath: string, type: string = 'image') => 
    request.upload({
      url: '/common/upload',
      filePath,
      name: 'file',
      formData: { type },
    }),

  // 获取系统配置
  getSystemConfig: () => 
    request.get<{
      practiceConfig: {
        questionsPerSession: number
        freeSessionsPerDay: number
      }
      examConfig: {
        faceVerifyMaxRetry: number
        autoSubmitBeforeEnd: number
        maxSwitchCount: number
      }
    }>('/common/config'),
}

// 导出所有API
export default {
  user: userApi,
  info: infoApi,
  study: studyApi,
  exam: examApi,
  personal: personalApi,
  common: commonApi,
}
